package com.ruoyi.graph;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.BdzNewLineInfo;
import com.ruoyi.entity.device.vo.FeederVo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.util.ListUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.support.ManagedMap;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 节点路径查找
 */
@Data
public class NodePath {
    public NodePath() {

    }

    Map<String, Node> nodeMap;

    Map<String, Node> nodeIdMap;

    ArrayList<Node> nodeList;

    Node startNode;

    Node startNextEdge;

    /**
     * 主干路径
     */
    List<ArrayList<Node>> mainNodePaths = new ArrayList<>();

    /**
     * 主干开关
     */
    List<Node> kgMainNodes = new ArrayList<>();

    /**
     * 所有的路径集合
     */
    List<ArrayList<Node>> allNodePaths;

    /**
     * 联络开关节点集合
     */
    List<Node> contactKgNodes;

    /**
     * 关联的联络联络线路开关和名称
     */
    private List<ContactFeederKg> contactFeederKgs;

    /**
     * 联络开关对应的方向节点
     */
    HashMap<String, Node> contactDirNodeMap = new HashMap<>();

    /**
     * 分段
     */
    ArrayList<SegBetween> segBetweenList = new ArrayList<>();

    /**
     * 开始的主干开关对应的集合
     */
    HashMap<String, List<SegBetween>> segBetweenGroupMap = new HashMap<>();

    /**
     * 分支节点路径
     */
    /*    ArrayList<BranchNode> branchNodeList = new ArrayList<>();*/

    /**
     * 分支节点路径（仅仅不包含主干路径的）
     */
    HashMap<String, BranchNode> branchNodeMap = new HashMap<>();

    /**
     * 分支节点路径（所有）
     */
    HashMap<String, BranchNode> branchAllNodeMap = new HashMap<>();

    /**
     * 节点对应的路径长度
     */
    HashMap<String, Integer> nodePathSize = new HashMap<>();

    /**
     * 路径末端节点对呀路径集合
     */
    HashMap<String, List<Node>> endNodePathsMap = new HashMap<>();

    /**
     * 分析路径
     *
     * @param startNode      开始节点
     * @param kgContactNodes 联络开关节点集合
     */
    public void analysisPath(Node startNode, List<Node> kgContactNodes, List<ContactFeederKg> contactFeederKgs) {
        nodeList = new ArrayList<>();
        nodeMap = new HashMap<>();
        nodeIdMap = new HashMap<>();

        contactDirNodeMap = new HashMap<>();
        this.startNode = startNode;
        nodePathSize = new HashMap<>();
        endNodePathsMap = new HashMap<>();

        HashMap<String, Node> keepNodeMap = new HashMap<>();

        this.contactKgNodes = new ArrayList<>(kgContactNodes);
        this.contactFeederKgs = new ArrayList<>(contactFeederKgs);
        Map<String, ContactFeederKg> contactFeederKgMap = this.contactFeederKgs.stream().collect(Collectors.toMap(ContactFeederKg::getKgPsrId, d -> d));
        allNodePaths = findPath(startNode, null, new ArrayList<>(), null, (Node node, Node beforeNode) -> {

            // 自定义设备 如果没有psrId这里设备
            if (!node.isPsrNode()) {
                if (StringUtils.isBlank(node.getPsrId())) {
                    node.setPsrId(node.getId());
                }
            }

            // 添加节点
            pushNode(node, keepNodeMap);
            pushNode(node.getParent(), keepNodeMap);
            for (Node child : node.getAllChildList()) {
                pushNode(child, keepNodeMap);
            }

            // TODO 按道理 遇到分闸开关 就终止  但是呢 没有开关的状态  我们就默认遇到联络开关终止

            // 联络线路开关  由于接口会把一个站房两个母线 连接在一起的联络开关放在一起 但其实对应我们这条线路来说 一个就行  不过这里直接跳过也不会加载到后面的了
            ContactFeederKg contactFeederKg = getCFeederKg(node, contactFeederKgMap);
            if (contactFeederKg != null) {
                this.contactKgNodes.add(node);
                this.contactFeederKgs.add(contactFeederKg);

                if (!contactFeederKg.isSource()) {
                    contactDirNodeMap.put(node.getPsrId(), beforeNode);
                }

                // 自定义联络开关 联络站房的不需要终止 以便后续方案生成
                if (!node.isContactStationInNode() && !contactFeederKg.isSource()) {
                    return true;
                }
            }

            return false;
        });

        // 处理下一个开始节点边
        allNodePaths.sort((d1, d2) -> d2.size() - d1.size());
        this.startNextEdge = allNodePaths.get(0).get(1);

        // 过滤没有边路出现过的开关
        this.contactKgNodes = ListUtils.distinctByKey(this.contactKgNodes, Node::getPsrId).stream().filter(d -> d != null && nodeMap.containsKey(d.getPsrId())).collect(Collectors.toList());
        this.contactFeederKgs = ListUtils.distinctByKey(this.contactFeederKgs, ContactFeederKg::getKgPsrId).stream().filter(d -> d != null && nodeMap.containsKey(d.getKgPsrId())).collect(Collectors.toList());

        // 过滤那些不能被转供联络的开关 例如配电室里面那些，这些联络开关都是的站房都是专门提供给用户  都不是具备联络功能的
        this.contactKgNodes = this.contactKgNodes.stream().filter(NodeUtils::canContactKgNode).collect(Collectors.toList());
        this.contactFeederKgs = this.contactFeederKgs.stream().filter(d -> NodeUtils.canContactKgNode(nodeMap.get(d.getKgPsrId()))).collect(Collectors.toList());

        // 先查找联络开关
        List<ArrayList<Node>> branchNodePaths = new ArrayList<>();

        // 生成主干路径
        for (int i = 0; i < allNodePaths.size(); i++) {
            ArrayList<Node> paths = allNodePaths.get(i);

            // 处理路径大小
            for (int j = 0; j < paths.size(); j++) {
                Node node = paths.get(j);
                if (StringUtils.isNotBlank(node.getPsrId())) {
                    nodePathSize.put(node.getPsrId(), j);
                }

                if (j == paths.size() - 1) {
                    endNodePathsMap.put(node.getId(), paths);
                }
            }

            // 查找联络路径的下标
            int contactKgIndex = NodeUtils.indexPathNode(paths, this.contactKgNodes);

            // 截取联络开关路径
            ArrayList<Node> mainSubPaths = contactKgIndex > -1 ? new ArrayList<>(paths.subList(0, contactKgIndex + 1)) : null;
            // 如果是联络开关路径的
            if (mainSubPaths != null) {
                Node contactNode = paths.get(contactKgIndex);
                //  表示重复的
                if (!mainNodePaths.stream().anyMatch(ps -> ps.size() == mainSubPaths.size() && NodeUtils.deepEqualPaths(ps, mainSubPaths))) {
                    mainNodePaths.add(mainSubPaths);
                }
                contactDirNodeMap.put(contactNode.getPsrId(), paths.get(contactKgIndex - 1));
            } else {
                branchNodePaths.add(paths);
            }

        }

        Map<String, SegBetween> segBetweenMap = new HashMap<>();

        // 处理主干开关和分段
        for (ArrayList<Node> mainNodePath : mainNodePaths) {
            // 上一个主干开关
            Node prevMainKg = null;
            ArrayList<Node> betweenNodes = new ArrayList<>();
            for (Node node : mainNodePath) {
                boolean isMainKg = node.isKg("all");
                // 主干路径上的开关
                if (isMainKg && node.getPsrId() != null) {
                    // 主干开关保存
                    if (!kgMainNodes.contains(node)) {
                        kgMainNodes.add(node);
                    }
                    betweenNodes.add(node);
                    // 处理分段
                    if (prevMainKg != null) {
                        String segId = SegBetween.getSegId(prevMainKg.getPsrId(), node.getPsrId());
                        // 表示当前分段还没有处理
                        if (!segBetweenMap.containsKey(segId)) {
                            // 处理
                            SegBetween segBetween = new SegBetween(segId, prevMainKg.getPsrId(), prevMainKg.getPsrType(), prevMainKg.getPsrName(), node.getPsrId(), node.getPsrType(), node.getPsrName());
                            segBetween.setStartNodeId(prevMainKg.getId());
                            segBetween.setEndNodeId(node.getId());
                            // 处理线路
                            segBetweenMap.put(segId, segBetween);
                            segBetween.setNodes(betweenNodes);
                            segBetweenList.add(segBetween);
                        }
                    }
                    prevMainKg = node;
                    betweenNodes = new ArrayList<>();
                    betweenNodes.add(node);
                } else {
                    if (prevMainKg != null) {
                        betweenNodes.add(node);
                    }
                }
            }
        }

        // 设置始的主干开关对应的集合
        for (SegBetween segBetween : segBetweenList) {
            String startId = segBetween.getStartPsrId();
            if (!segBetweenGroupMap.containsKey(startId)) {
                segBetweenGroupMap.put(startId, new ArrayList<>(Arrays.asList(segBetween)));
            } else {
                segBetweenGroupMap.get(startId).add(segBetween);
            }
        }

        // 根据路径设置分段的mainOtherNodeMap节点集合 和 分支设备集合
        handleSegOtherNodesAndBranchNodes(branchNodePaths);

        // 所有分叉分支节点和分段mainAllOtherNodes节点处理
        handleAllSegOtherNodesAndBranchNodes();
    }

    // 获取或者创建联络线路开关
    ContactFeederKg getCFeederKg(Node node, Map<String, ContactFeederKg> contactFeederKgMap) {
        ContactFeederKg contactFeederKg = contactFeederKgMap.get(node.getPsrId());
        if (contactFeederKg != null) {
            return contactFeederKg;
        }

        String[] cFeederStr = node.getContactFeeder();
        // TODO 环网柜剩余间隔联络线出线 的开关 可能不是联络开关  应该是进线或者 当前环网柜下的已有的联络开关
        if (cFeederStr != null) {
            contactFeederKg = new ContactFeederKg(node.getPsrId(), node.getPsrType(), node.getPsrName());
            contactFeederKg.setFeederPsrId(cFeederStr[0]);
            contactFeederKg.setFeederPsrName(cFeederStr[1]);
            contactFeederKg.setSource(false);
            return contactFeederKg;
        }

        BdzNewLineInfo bdzNewLine = node.getBdzNewLine();
        if (bdzNewLine != null) {
            contactFeederKg = new ContactFeederKg(node.getPsrId(), node.getPsrType(), node.getPsrName());
            contactFeederKg.setBdzId(bdzNewLine.getBdzPsrId());
            contactFeederKg.setBdzName(bdzNewLine.getBdzPsrName());
            contactFeederKg.setSource(false);
            return contactFeederKg;
        }

        return null;
    }

    /**
     * 获取路径最后主干开关出现的下标
     *
     * @param paths 路径节点
     */
    int getLastMainKgIndex(ArrayList<Node> paths) {
        int result = -1;
        for (int i = 0; i < paths.size(); i++) {
            Node node = paths.get(i);
            // 如果是开关 并且是主干开关
            if (node.isKg("all") && segBetweenGroupMap.containsKey(node.getPsrId())) {
                result = i;
            }
        }
        return result;
    }

    /**
     * 该接口返回所有可以走的路径
     *
     * @param startNode     开始节点
     * @param endNode       结束节点
     * @param parentPaths   父级路径集合
     * @param nextOnlyEdges 当前设备深度递归时  分叉节点 下一个边集合
     */
    public static List<ArrayList<Node>> findPath(Node startNode, Node endNode, ArrayList<Node> parentPaths, ArrayList<Node> nextOnlyEdges, BiFunction<Node, Node, Boolean> appFunc) {
        Node currentNode = startNode;
        ArrayList<Node> paths = new ArrayList<>(parentPaths);

        // 递归获取查找
        while (currentNode != null) {
            // 新增当前的线路
            paths.add(currentNode);
            Node beforeNode = null;
            if (paths.size() >= 2) {
                beforeNode = paths.get(paths.size() - 2);
            }
            Boolean isBreak = appFunc.apply(currentNode, beforeNode);
            boolean isEdge = currentNode.isEdge();
            Node nextNode = null;
            if (endNode != null && endNode.equals(currentNode.getId())) {
                break;
            }
            List<Node> edges = null;
            if (isEdge) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = paths.contains(source) ? target : source;
                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }

                if (isBreak) {
                    if (nextNode != null) {
                        // 取消关联关系
                        currentNode.removeLink(nextNode);
                    }
                    break;
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }

            if (edges != null) {
                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !paths.contains(n)).collect(Collectors.toList());
                ArrayList<Node> nextEdges = null;

                if (isBreak) {
                    // 取消关联关系
                    for (Node edge : edges) {
                        currentNode.removeEdge(edge);
                    }
                    break;
                }

                // 过滤仅仅需要的下一个边
                if (CollectionUtils.isNotEmpty(nextOnlyEdges)) {
                    edges = edges.stream().filter(n -> nextOnlyEdges.contains(n)).collect(Collectors.toList());
                    List<Node> finalEdges = edges;
                    nextEdges = (ArrayList<Node>) nextOnlyEdges.stream().filter(n -> !finalEdges.contains(n)).collect(Collectors.toList());
                }

                List<ArrayList<Node>> result = new ArrayList<>();

                // 调度继续循环
                for (Node edge : edges) {
                    List<ArrayList<Node>> tmpPaths = findPath(edge, endNode, paths, nextEdges, appFunc);
                    // 如果是二维数组（表示数组里面条路径了）

                    // 否则就是当前单条路径
                    if (CollectionUtils.isNotEmpty(tmpPaths)) {
                        result.addAll(tmpPaths);
                    }
                }
                if (!result.isEmpty()) {
                    return result;
                }
            }
            currentNode = nextNode;
        }

        List<ArrayList<Node>> result = new ArrayList<>();
        result.add(paths);

        return result;
    }

    /**
     * 设置各个分支节点 根据路径
     */
    void setBranchNodeMap(ArrayList<Node> paths, HashMap<String, BranchNode> branchNodeMap, int endIndex, HashMap<String, Node> mainNodeMap) {
        ArrayList<Node> nodes = new ArrayList<>();

        endIndex = endIndex == -1 ? 0 : endIndex;

        for (int i = paths.size() - 1; i > endIndex; i--) {
            Node afterNode = paths.get(i);
            Node before = paths.get(i - 1);
            nodes.add(afterNode);
            if ((i - 1) == endIndex) {
                nodes.add(before);
            }
            List<Node> edges = before.getEdges();
            if (CollectionUtils.isNotEmpty(edges) && edges.size() > 2 && !before.isUserPoint()) {
                String branchId = BranchNode.processId(before, afterNode);
//                if (before.isBus()) {
//                    nodes.remove(afterNode);
//                    nodes.remove(before);
//
//                    Node tmpNode = afterNode;
//                    before = BranchNode.getNextNode(before, afterNode);
//                    List<Node> otherEdges = before.getEdges().stream().filter(n -> !n.equals(tmpNode.getId())).collect(Collectors.toList());
//
//                    if (StringUtils.equals(before.getPsrId(), "daeb4b47-6cdc-4d3e-8e7c-6440613698fd")) {
//                        System.out.println(before);
//                    }
//
//                    if (otherEdges.isEmpty()) {
//                        afterNode = null;
//                    } else {
//                        afterNode = otherEdges.get(endIndex);
//                    }
//                    branchId = BranchNode.processId(before, afterNode);
//                }

                // 设置分支节点
                BranchNode branchNode = branchNodeMap.get(branchId);
                if (branchNode == null) {
                    Node nextNode = afterNode; // BranchNode.getNextNode(before, afterNode);

                    // 当前节点在主干路径上  就是
                    boolean isMain = mainNodeMap.get(before.getPsrId()) != null;

                    // 开始节点
                    branchNode = new BranchNode(branchId, before.getPsrId(), before.getPsrType(), before.getPsrName(), nextNode.getId(), isMain);
                    branchNodeMap.put(branchId, branchNode);
                }
                List<Node> bNodes = branchNode.getNodes();
                List<Node> pbNodes = branchNode.getPbNodes();
                Map<String, Node> nodeMap = bNodes.stream().collect(Collectors.toMap(Node::getId, node -> node));
                for (Node node : nodes) {
                    if (!nodeMap.containsKey(node.getId())) {
                        bNodes.add(node);
                        if (node.isPb()) {
                            pbNodes.add(node);
                        }
                    }
                }

            }
        }
    }

    // 获取节点map根据节点路径集合
    HashMap<String, Node> getNodeMap(List<ArrayList<Node>> paths) {
        HashMap<String, Node> mainNodeMap = new ManagedMap<>();
        for (ArrayList<Node> mainNodePath : paths) {
            for (Node node : mainNodePath) {
                if (StringUtils.isNotBlank(node.getPsrId())) {
                    mainNodeMap.put(node.getPsrId(), node);
                }
            }

        }
        return mainNodeMap;
    }

    // =========================== 分叉分支节点和分段mainOtherNodeMap节点处理 =======================

    /**
     * 根据路径设置分段的mainOtherNodeMap节点集合 和 分支设备集合
     *
     * @param branchNodePaths 分支路径
     */
    void handleSegOtherNodesAndBranchNodes(List<ArrayList<Node>> branchNodePaths) {
        for (ArrayList<Node> paths : branchNodePaths) {
            // 获取重叠的路径
            // 获取路径最后的主干开关下标
            int lastMainKgIndex = getLastMainKgIndex(paths);
            if (lastMainKgIndex > -1) {
                if (StringUtils.equals("SBID000000BB1C3BF09D57475EBC32D444C41C767A", paths.get(lastMainKgIndex).getPsrId())) {
                    System.out.println(paths);
                }
                List<SegBetween> segBetweenList = segBetweenGroupMap.get(paths.get(lastMainKgIndex).getPsrId());
                if (CollectionUtils.isNotEmpty(segBetweenList)) {
                    for (SegBetween segBetween : segBetweenList) {
                        //根据路径设置分段的mainOtherNodeMap节点集合 和 分支设备集合
                        setSegAndBranchNodes(segBetween, paths, lastMainKgIndex);
                    }
                }
            }
        }
    }

    /**
     * 根据路径设置分段的mainOtherNodeMap节点集合 和 分支设备集合
     *
     * @param paths           路径
     * @param lastMainKgIndex 主干路径分叉下标
     */
    void setSegAndBranchNodes(SegBetween segBetween, ArrayList<Node> paths, int lastMainKgIndex) {
        ArrayList<Node> segNodes = segBetween.getNodes();
        List<Node> mainOtherNodes = segBetween.getMainOtherNodes();

        HashMap<String, Node> mainNodeMap = getNodeMap(new ArrayList<>(Collections.singletonList(segNodes)));

        Map<String, Node> mainOtherNodeMap = mainOtherNodes.stream().collect(Collectors.toMap(Node::getId, node -> node));

        int endIndex = lastMainKgIndex;

        // 获取与主干开关重叠的下标
        for (int i = lastMainKgIndex + 1; i < paths.size(); i++) {
            Node node = paths.get(i);
            // 表示开始有重叠
            int segIndex = i - lastMainKgIndex;
            Node segNode = segIndex < segNodes.size() ? segNodes.get(segIndex) : null;
            if (segNode != null && segNode.equals(node)) {
                endIndex = i;
            } else {
                break;
            }
        }

        if (StringUtils.equals("SBID0000003B93409FE49046EE9D077CE3C3E0705E", segBetween.getEndPsrId())) {
            System.out.println(segNodes);
        }

        // 如果当前路径横跨这个分段 那么就不需要设置（该路径必须在分段内）
        boolean isBetween = paths.stream().noneMatch(d -> StringUtils.equals(d.getPsrId(), segBetween.getEndPsrId()));

        if (isBetween) {
            // 条件到设备里面
            for (int i = endIndex; i < paths.size(); i++) {
                Node node = paths.get(i);
                // 将当前设备加入到主干估计上
                if (!mainOtherNodeMap.containsKey(node.getId())) {
                    mainOtherNodes.add(node);
                }
            }
        }


        setBranchNodeMap(paths, this.branchNodeMap, endIndex, mainNodeMap);
    }


    // =========================== 所有分叉分支节点和分段mainAllOtherNodes节点处理 =======================

    /**
     * 处理各个分支节点
     */
    void handleAllSegOtherNodesAndBranchNodes() {

        // 处理各个分支
        HashMap<String, Node> mainNodeMap = getNodeMap(mainNodePaths);

        // 处理所有分支线
        for (ArrayList<Node> paths : allNodePaths) {
            setBranchNodeMap(paths, this.branchAllNodeMap, 0, mainNodeMap);
        }

        // 处理所有的分段
        handleSegAllOtherNodes();
    }

    /**
     * 处理分段节点的其他节点(mainAllOtherNodes)集合
     */
    void handleSegAllOtherNodes() {
        for (SegBetween segBetween : segBetweenList) {
            ArrayList<Node> pathNodes = segBetween.getNodes();
            List<Node> mainAllOtherNodes = new ArrayList<>();

            // 当前分段的主干路径
            for (Node node : pathNodes) {
                List<Node> edges = node.getEdges();
                if (CollectionUtils.isNotEmpty(edges) && edges.size() > 2) {
                    //  确定是否需要注意母线情况

                    // 表示主干路径上有分支线 处理该分支节点
                    // 排除主干路径的边
                    edges = edges.stream().filter(n -> !pathNodes.contains(n)).collect(Collectors.toList());
                    for (Node edge : edges) {
                        // 获取该路径的分支
                        BranchNode branchNode = branchAllNodeMap.get(BranchNode.processId(node, edge));
                        // 将改分支点节点添加到里面
                        if (branchNode != null) {
                            mainAllOtherNodes.addAll(branchNode.getNodes());
                        }
                    }
                }
            }
            // 先去重
            mainAllOtherNodes = ListUtils.distinctByKey(mainAllOtherNodes, Node::getId);
            segBetween.setMainAllOtherNodes(new ArrayList<>(mainAllOtherNodes));
        }
    }

    // =========================== 其它业务 =======================

    // 添加节点
    void pushNode(Node node, HashMap<String, Node> keepNodeMap) {
        if (node == null) {
            return;
        }
        if (!keepNodeMap.containsKey(node.getId())) {
            nodeList.add(node);
            nodeIdMap.put(node.getId(), node);
            if (StringUtils.isNotBlank(node.getPsrId())) {
                nodeMap.put(node.getPsrId(), node);
            }
            keepNodeMap.put(node.getId(), node);
        }
    }

    // 自定义开关
//    void custContactKg(Node node,List<Node> addContactKgs) {
//        addContactKgs.add(node);
//        String[] contactFeeder = node.getContactFeeder();
//        ContactFeederKg contactFeederKg = new ContactFeederKg(node.getPsrId(), node.getPsrType(), node.getPsrName());
//        contactFeederKg.setFeederPsrId(contactFeeder[0]);
//        contactFeederKg.setFeederPsrName(contactFeeder[1]);
//        addContactFeederKgs.add(contactFeederKg);
//    }

    /**
     * 获取联络开关在此路径上所有与主干开关组成的所有分段
     */
    public List<SegBetween> getContactKgAllSeg(String contactKgPsrId) {
        for (ArrayList<Node> mainNodePath : mainNodePaths) {
            if (mainNodePath.stream().anyMatch(n -> StringUtils.equals(contactKgPsrId, n.getPsrId()))) {
                return getContactKgAllSeg(mainNodePath, contactKgPsrId);
            }
        }
        return null;
    }

    /**
     * 获取联络开关在此路径上所有与主干开关组成的所有分段
     */
    public List<SegBetween> getContactKgAllSeg(List<Node> paths, String kgPsrId) {

        Node kgNode = null;
        int kgIndex = -1;
        // 获取开关节点和开关的下标
        for (int i = paths.size() - 1; i >= 0; i--) {
            Node node = paths.get(i);
            // 表示当前KG
            if (StringUtils.equals(kgPsrId, node.getPsrId())) {
                kgNode = node;
                kgIndex = i;
                break;
            }
        }

        if (kgIndex == -1) {
            return null;
        }

        ArrayList<SegBetween> result = new ArrayList<>();

        // 遍历路径分别获取联络开关和各个分段
        Node endNode = kgNode;
        List<SegBetween> beforeSegBetweens = new ArrayList<>();
        for (int i = kgIndex - 1; i >= 0; i--) {
            Node startNode = paths.get(i);
            // 主干路径的开关 需要和联络开关做分段
            if (startNode.isKg("all")) {
                String endPsrId = endNode.getPsrId();
                // 获取当前主干开关和上一个节点的分段对象
                List<SegBetween> segBetweens = segBetweenGroupMap.get(startNode.getPsrId());// 当前分段集合
                SegBetween matchSegBetween = CollectionUtils.isNotEmpty(segBetweens) ? ListUtils.findFirst(segBetweens, n -> StringUtils.equals(n.getEndPsrId(), endPsrId)) : null;

                if (matchSegBetween != null) {
                    beforeSegBetweens.add(matchSegBetween);

                    // 创建联络开关到当前节点的分段
                    String segId = SegBetween.getSegId(startNode.getPsrId(), kgNode.getPsrId());
                    SegBetween segBetween = new SegBetween(segId, startNode.getPsrId(), startNode.getPsrType(), startNode.getPsrName(), kgNode.getPsrId(), kgNode.getPsrType(), kgNode.getPsrName());
                    segBetween.setStartNodeId(startNode.getId());
                    segBetween.setEndNodeId(kgNode.getId());

                    // 设置这期间的节点集合
                    ArrayList<Node> nodes = new ArrayList<>();
                    ArrayList<Node> mainOtherNodes = new ArrayList<>();
                    // 之前的分段节点集合 合并
                    for (SegBetween beforeSegBetween : beforeSegBetweens) {
                        nodes.addAll(beforeSegBetween.getNodes());
                        mainOtherNodes.addAll(beforeSegBetween.getMainAllOtherNodes());
                    }
                    segBetween.setNodes(nodes);
                    segBetween.setMainOtherNodes(mainOtherNodes);
                    result.add(segBetween);
                }
                endNode = startNode;
            }
        }

        return result;
    }

    /**
     * 截取路径
     *
     * @param startPsrId 起始节点  不传默认是从电源开关开始设备ID节点
     * @param endPsrIds  结束的节点设备ID集合
     * @return
     */
    public HashMap<String, List<Node>> subPathByEndPsrIds(String startPsrId, List<String> endPsrIds) {
        HashMap<String, List<Node>> pathMap = new HashMap<>();
        for (ArrayList<Node> paths : allNodePaths) {
            List<Node> subPaths = new ArrayList<>();
            boolean started = false;
            for (int i = 0; i < paths.size(); i++) {
                Node node = paths.get(i);
                String endPsrId = node.getPsrId();
                // 如果没有开始节点或者开始节点相等那么就开始截取
                if (StringUtils.isBlank(startPsrId) || StringUtils.equals(startPsrId, endPsrId)) {
                    started = true;
                }

                if (started == true) {
                    subPaths.add(node);
                }
                // 如果结束的节点已经有
                int index = endPsrIds.indexOf(endPsrId);
                if (index != -1) {
                    pathMap.put(endPsrId, subPaths);
                    endPsrIds.remove(index);
                    break;
                }
                if (endPsrIds.isEmpty()) {
                    break;
                }
            }
        }
        return pathMap;
    }

    /**
     * 获取所有的配变
     */
    public List<Node> getPbList() {
        return nodeList.stream().filter(Node::isPb).collect(Collectors.toList());
    }

    /**
     * 根据设备ID获取对应的节点
     */
    public Node getNodeByPsrId(String psrId) {
        return nodeMap.get(psrId);
    }

    /**
     * 根据设备ID获取对应的节点
     */
    public List<Node> getPathsByEndId(String nodeId) {
        return endNodePathsMap.get(nodeId);
    }

    /**
     * 获取联络线Id集合
     */
    public List<String> getContactFeederIds() {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactFeederKgs)) {
            return result;
        }

        result = contactFeederKgs.stream().map(ContactFeederKg::getFeederPsrId).collect(Collectors.toList());

        // 去重
        return ListUtils.distinctByKey(result, n -> n);
    }

    /**
     * 获取联络线Id集合
     */
    public List<FeederVo> getContactFeederVos() {
        List<FeederVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(contactFeederKgs)) {
            return result;
        }

        result = contactFeederKgs.stream().map(d -> new FeederVo(d.getFeederPsrId(), d.getFeederPsrName())).collect(Collectors.toList());

        // 去重
        return ListUtils.distinctByKey(result, FeederVo::getFeederId);
    }

    /**
     * 获取联络开关的方向节点
     *
     * @return
     */
    public Node getContactNodeDirNode(Node heNode) {
        Node dirNode = contactDirNodeMap.get(heNode.getPsrId());
        if (dirNode != null) {
            return dirNode;
        }
        // 没有表示可能是方案带的联络开关  需要优化处理一下
        if (heNode.isContactStationInNode()) {
            for (Node child : heNode.getParent().getChildren()) {
                if (child == null) {
                    continue;
                }
                if (!child.equals(heNode.getId()) && getContactKgNodes().stream().anyMatch(d -> d.equals(child.getId()))) {
                    return contactDirNodeMap.get(child.getPsrId());
                }
            }
        }
        return null;
    }
}
