package com.ruoyi.graph;

import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.graph.utils.NodeUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 分支节点
 */
@Data
public class BranchNode {
    public BranchNode(String psrId, String psrType) {
        this.psrId = psrId;
        this.psrType = psrType;
    }

    public BranchNode(String id, String psrId, String psrType, String psrName, String nextId, boolean isMain) {
        this.id = id;
        this.psrId = psrId;
        this.psrType = psrType;
        this.nextId = nextId;
        this.isMain = isMain;
        this.psrName = psrName;
    }

    private String id;

    /**
     * 分支节点设备ID
     */
    private String psrId;

    /**
     * 分支节点类型
     */
    private String psrType;

    /**
     * 分支节点类型
     */
    private String psrName;

    /**
     * 分支节点设备ID
     */
    private String nextId;

    /**
     * 是否主干路径的分支(大分分支)
     */
    private boolean isMain;

    /**
     * 该分支下的节点集合
     */
    List<Node> nodes = new ArrayList<>();

    /**
     * 该分支下的配变节点集合
     */
    List<Node> pbNodes = new ArrayList<>();

    /**
     * 配变数量
     */
    private Integer pbNum = 0;

    /**
     * 获取当前分支下的节点集合数量
     */
    public int getNodeNum() {
        return nodes.size();
    }

    /**
     * 获取当前分支下的配变节点集合数量
     */
    public int getPbNodeNum() {
        return pbNodes.size();
    }

    public static String processId(Node node, Node after) {
        return node.getPsrId() + "_" + after.getId();
    }


    /**
     * 获取大分子路径
     */
    public List<Node> getBranchPath() {
        // 大分子路径
        return nodes.stream().filter(n -> n.getPsrId() != null && n.isSegFeeder()).collect(Collectors.toList());
    }

    /**
     * 获取开始的节点  如果是母线 那么我们就取下一个
     */
    public Node getStartNode() {
        String startPsrId;
        // 表示母线
        if (DeviceConstants.BUS_TYPES.contains(psrType)) {
            Node node = NodeUtils.findNode(nodes, (n) -> StringUtils.equals(n.getId(), nextId));
            if (node != null) {
                return node.getTarget();
            }
            return null;
        } else {
            return NodeUtils.findNode(nodes, (n) -> StringUtils.equals(n.getPsrId(), psrId));
        }
    }

    /**
     * 获取开始到第一个分叉节点的路径
     */
    public List<Node> getStartToFirstBranchPaths(NodePath nodePath) {
        Map<String, Node> nodeMap = nodePath.getNodeMap();
        Map<String, Node> nodeIdMap = nodePath.getNodeIdMap();

        Node startNode = nodeMap.get(psrId);
        Node nextNode = nodeIdMap.get(nextId);

        List<Node> paths = new ArrayList<>();
        NodeUtils.loopNode(startNode, new HashMap<>(), Collections.singletonList(nextNode), (node) -> {
            paths.add(node);
            return !node.equals(startNode) && CollectionUtils.isNotEmpty(node.getEdges()) && node.getEdges().size() > 2;
        });
        return paths;
    }
}
