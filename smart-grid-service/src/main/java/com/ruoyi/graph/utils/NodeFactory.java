package com.ruoyi.graph.utils;

import com.ruoyi.graph.Node;
import org.locationtech.jts.geom.*;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 节点工厂
 */
@Component
public class NodeFactory {

    GeometryFactory geometryFactory = new GeometryFactory();

    public Node createDevice(String id, double lng, double lat) {

        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));
        Node node = new Node(id, point);
        node.setEdge(false);

        return node;
    }

    public Node createEdge(String id, double[][] coords) {

        Coordinate[] collectList = Arrays.stream(coords)
                .map(lngLat -> new Coordinate(lngLat[0], lngLat[1]))
                .toArray(Coordinate[]::new);
        LineString lineString = geometryFactory.createLineString(collectList);
        Node node = new Node(id, lineString);
        node.setEdge(true);

        return node;
    }

    static public Node createNode(String id, String psrId, String psrType) {
        Node node = new Node(id, psrId, psrType);
        node.setProperties(new HashMap<String, Object>() {{
            put("psrId", psrId);
            put("psrType", psrType);
        }});
        return node;
    }

    static public Node createNode(String id, Geometry geometry) {
        return new Node(id, geometry);
    }

}


