package com.ruoyi.mapper.device;

import com.ruoyi.common.core.mapper.BaseMapperPlus;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.power.DeviceApiJson;
import com.ruoyi.entity.problem.ProblemGrid;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

public interface DeviceApiJsonMapper extends BaseMapperPlus<DeviceApiJsonMapper, DeviceApiJson, DeviceApiJson> {

    @Insert({
            "INSERT INTO device_api_json (grid_code, json) ",
            "VALUES (#{gridCode}, #{json}::jsonb)"
    })
    void insert(@Param("gridCode")String gridCode, @Param("json")String json);


    @Insert({
            "INSERT INTO resource_statistics_data (grid_code,type,json) ",
            "VALUES (#{gridCode},#{type}, #{json}::jsonb)"
    })
    void insertPowerGridVo(@Param("gridCode")String gridCode, @Param("type")Integer type, @Param("json")String json);

    @Select("SELECT count(*) from resource_statistics_data where grid_code = #{gridCode}")
    Integer selectCount(@Param("gridCode")String gridCode);

    @Delete("DELETE from resource_statistics_data where grid_code = #{gridCode} ")
    void deleteGrodCode(@Param("gridCode")String psrId);
    @Select("SELECT json from resource_statistics_data where grid_code = #{gridCode} AND type = #{base} ")
    String selectPowerGridVo(@Param("gridCode")String code,@Param("base") Integer base);
}
