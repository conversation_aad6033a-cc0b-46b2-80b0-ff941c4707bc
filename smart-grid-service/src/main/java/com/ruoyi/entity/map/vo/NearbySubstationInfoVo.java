package com.ruoyi.entity.map.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 附近变电站信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class NearbySubstationInfoVo {


    /**
     * 剩余间隔数量（预留 + 备用）
     */
    private Integer remainingBayCount;

    /**
     * 电压等级
     */
    private String voltageLevel;


    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    private String psrId;
    /**
     * 资产ID
     */
    private String astId;
    /**
     * 设备名称
     */
    private String name;
    /**
     * 站房类型
     */
    private String stationType;
    /**
     * 运行编号
     */
    private String runDevName;

    /**
     * 所属地市
     */
    private String city;
    /**
     * 运维单位
     */
    private String maintOrg;
    /**
     * 维护班组
     */
    private String maintGroup;
    /**
     * 设备主人
     */
    private String equipmentOwner;
    /**
     * 运行状态
     */
    private String psrState;
    /**
     * 投运日期
     */
    private Date startTime;

    /**
     * 重要级别
     */
    private String importantLevel;
    /**
     * 是否农网
     */
    private String isRural;
    /**
     * 地区特征
     */
    private String regionalism;
    /**
     * 供电区域
     */
    private String supplyArea;

    private double distance;
    /**
     * 值班方式
     */
    private String dutyMode;

    /**
     * 电站海拔
     */
    private String altitude;
    /**
     * 占地面积
     */
    private Double coverArea;

    /**
     * 电站地址
     */
    private String address;
    /**
     * 污秽等级
     */
    private String contaminationLevel;
    /**
     * 主变台数
     */
    private Long transformerQuantity;
    /**
     * 电站容量
     */
    private Double stationCapacity;
    /**
     * 是否GIS站
     */
    private String isGis;

    /**
     * 是否枢纽站
     */
    private String isJunctionStation;
    /**
     * 是否智能站
     */
    private String isSmartStation;


    /**
     * 是否集中监控
     */
    private String isCentralMonitor;
    /**
     * 布置方式
     */
    private String arrangement;


    /**
     * 坐标位置
     */
    private String geoPositon;
    /**
     * 消防验收情况
     */
    private String fireAcceptance;
    /**
     * 站房消防类型
     */
    private String fireType;

    /**
     * 关联d5000数据库的变电站id
     */
    private String sgId;

    /**
     * 创建时间
     */
    private Date ctime;
    /**
     * 最后更新时间
     */
    private Long lastUpdateTime;

    /**
     * 开关信息
     */
    private List<BusbarSwitchVo> busbarSwitchVoList;

    private double[] lngLat;

    /**
     * 获取剩余可以的间隔（多个我们就取第一个即可）
     */
    public BusbarSwitchVo getCanBay() {
        if (CollectionUtils.isEmpty(busbarSwitchVoList)) {
            return null;
        }
        List<BusbarSwitchVo> remainingBays = busbarSwitchVoList.stream().filter(BusbarSwitchVo::getIsSpare).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(remainingBays)) {
            return null;
        }
        return remainingBays.get(0);
    }
}
