package com.ruoyi.entity.calc;

import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeUtils;
import lombok.Data;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 线路负荷转供
 */
@Data
public class FeederTransferCap {
    public FeederTransferCap() {
    }

    public FeederTransferCap(String sourcePsrId, String sourcePsrName, String tfContactPsrId, String tfContactPsrName) {
        this.sourcePsrId = sourcePsrId;
        this.sourcePsrName = sourcePsrName;
        this.tfContactPsrId = tfContactPsrId;
        this.tfContactPsrName = tfContactPsrName;
    }

    /**
     * 分闸和和闸开关
     */
    Node fenNode, heNode;

    /**
     * 原线路
     */
    String sourcePsrId, sourcePsrName;

    /**
     * 专供线路ID
     */
    String tfContactPsrId, tfContactPsrName;


    /**
     * 原线路负载率
     */
    double sourceLoad;

    /**
     * 专供之后原线路的负载率
     */
    double sourceChangeLoad;

    /**
     * 转供联络线路负载率
     */
    double tfContactLoad;

    /**
     * 专供之后转供联络线路的负载率
     */
    double tfContactChangeLoad;

    /**
     * 原线路的配变数量
     */
    int sourcePbNum;

    /**
     * 转供线路的配变数量
     */
    int tfContactPbNum;

    /**
     * 转供的路径
     */
    List<Node> paths;

    /**
     * 设置分合节点
     */
    public void generateFenHeNode(Node heNode, Node fenNode) {

        heNode = heNode.clone();
        heNode.setSwitchOpen(false);
        heNode.setRunAdjustAnother(fenNode.getPsrId());
        this.heNode = heNode;

        fenNode = fenNode.clone();
        fenNode.setSwitchOpen(true);
        fenNode.setRunAdjustAnother(heNode.getPsrId());
        this.fenNode = fenNode;
    }

    public boolean equals(FeederTransferCap fTr) {
        return heNode.equals(fTr.getHeNode()) && fenNode.equals(fTr.getFenNode());
    }

    /**
     * 获取减少点负载率
     */
    public double getDecrLoad() {
        return sourceLoad - sourceChangeLoad;
    }

    /**
     * 获取转供线路新增的负载率
     */
    public double getIncrTfContactLoad() {
        return tfContactChangeLoad - tfContactLoad;
    }

    /**
     * 获取转供的配变列表
     */
    public List<Node> getPbs() {
        return paths.stream().filter(Node::isPb).collect(Collectors.toList());
    }

    /**
     * 获取原路转供之后的配变数
     */
    public int getSourceAfterPbNum() {
        return sourcePbNum - getPbs().size();
    }

    /**
     * 获取转供联络线路转供之后的配变数
     */
    public int getTfContactAfterPbNum() {
        return tfContactPbNum + getPbs().size();
    }

    /**
     * 转为FeederTransferCapVo
     */
    public FeederTransferCapVo toFTrVo() {
        FeederTransferCapVo result = new FeederTransferCapVo(sourcePsrId, sourcePsrName, tfContactPsrId, tfContactPsrName);

        // 处理NodeVo
        result.setFenNode(NodeUtils.toNodeVo(fenNode));
        result.setHeNode(NodeUtils.toNodeVo(heNode));

        result.setSourceLoad(sourceLoad);
        result.setSourceChangeLoad(sourceChangeLoad);
        result.setTfContactLoad(tfContactLoad);
        result.setTfContactChangeLoad(tfContactChangeLoad);

        result.setSourcePbNum(sourcePbNum);
        result.setTfContactPbNum(tfContactPbNum);

        result.setPaths(NodeUtils.toNodeVos(paths));
        return result;
    }

}
