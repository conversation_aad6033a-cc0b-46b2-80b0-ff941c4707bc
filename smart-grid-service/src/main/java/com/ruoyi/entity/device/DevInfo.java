package com.ruoyi.entity.device;

import lombok.Data;

@Data
public class DevInfo {
    public DevInfo(String psrId, String psrType) {
        this.psrId = psrId;
        this.psrType = psrType;
    }

    public DevInfo(String psrId, String psrType, String psrName) {
        this.psrId = psrId;
        this.psrType = psrType;
        this.psrName = psrName;
    }

    /**
     * 设备psrId集合
     */
    private String psrId, psrType, psrName;

    /**
     * 站房
     */
    private String stationPsrId, stationPsrType, stationPsrName;

    private String feederId, feederName;

    public String getFeederId() {
        return feederId;
    }

    public void setFeederId(String feederId) {
        this.feederId = feederId;
    }

    public String getFeederName() {
        return feederName;
    }

    public void setFeederName(String feederName) {
        this.feederName = feederName;
    }

    private double[] lngLat;
}
