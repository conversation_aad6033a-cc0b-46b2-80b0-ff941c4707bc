package com.ruoyi.service.plan;

import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.vo.BusbarSwitchVo;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public interface IContactHandle {

    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOpMap, List<NearbyDeviceInfoVo> nearDevs, String token);

    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOpMap, List<NearbySubstationInfoVo> bdzBays, String token);

    default List<NearNode> toNearNodeList(List<Node> nodeList) {
        return nodeList.stream().map(this::toNearNode).collect(Collectors.toList());
    }

    default NearNode toNearNode(Node node) {
        return new NearNode(node, node.toDeviceCoords());
    }
}
