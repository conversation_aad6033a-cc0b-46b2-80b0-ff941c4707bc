package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.processNode.ProcessNearNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 杆塔联络线的处理
 */
@Component
public class PoleContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ProcessNearNode processNearNode;

    @Override
    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbyDeviceInfoVo> nearDevs, String token) {

        // 附近节点
        NearNode nearNode = toNearNode(lay);

        // 加工
        processNearNode.processDevRoute(nearNode, nearDevs, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = processContactHandle(nearNode, ProcessContactBo.POLE_RANGE);

        lay.setProcessContactVo(processContactVo);
    }

    @Override
    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbySubstationInfoVo> bdzBays, String token) {

        NearNode nearNode = toNearNode(lay);

        // 加工最近的变电站
        processNearNode.processBdzRoute(nearNode, bdzBays, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = processBdzNewLineHandle(nearNode, ProcessContactBo.POLE_RANGE);

        // 加装
        lay.setProcessContactVo(processContactVo);
    }

    public NearNode toNearNode(ContactLay lay) {
        return toNearNode(lay.getProcessContactBo().getPole());
    }
}
