package com.ruoyi.service.plan.processNode.layHandle;

import com.ruoyi.constant.NodeConstants;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

@Component
public class ProcessUtils {

    @Autowired
    NodeFactory nodeFactory;

    /**
     * 在杆塔上加装开关 左边链接线连接杆塔  右边连接导线段
     */
    public List<Node> generatePoleKg(Node pole, Node segLine, boolean isCopySource, Boolean switchOpen) {

        Geometry geometry = pole.getGeometry();
        Node target = null;
        Node segSource = segLine.getSource();
        Node segTarget = segLine.getTarget();
        boolean poleIsSource = true;

        // 表示当前线路与当前pole相同 那么需要真正设置poleIsSource
        if ((segSource != null && segSource.equals(pole.getId())) || (segTarget != null && segTarget.equals(pole.getId()))) {
            poleIsSource = segLine.getSource() != null && segLine.getSource().equals(pole.getId());
        }

        if (isCopySource) {
            pole = pole.clone();
            // 另一个目标设备
            target = segLine.getLink(!poleIsSource);
            if (target != null) {
                target = target.clone();
            }
            segLine = segLine.clone();
        }

        // 开始开关  左边链接线连接杆塔  右边连接导线段

        // 创建开关和开关和杆塔的连接线
        Node kg = nodeFactory.createNode(UUID.randomUUID().toString(), geometry.copy());
        kg.setType(Node.TYPE_SELF);
        kg.setShapeKey(NodeConstants.SHAPE_KEY_POLE_LOAD_KG);
        kg.setPsrType("0112");
        kg.setPsrName("柱上负荷开关");
        kg.setProperties(new HashMap<String, Object>() {{
            put("isKg", true);
            put("name", "柱上负荷开关");
            put("type", NodeConstants.SHAPE_KEY_POLE_LOAD_KG);
        }});
        kg.setPsrId(kg.getId());

        if (switchOpen != null) {
            kg.setSwitchOpen(switchOpen);
        }

        // 创建连接线
        Coordinate coordinate = geometry.getCoordinate();
        double[][] coords = {{coordinate.getX(), coordinate.getY()},
                {coordinate.getX(), coordinate.getY()}};
        Node edge = nodeFactory.createEdge(UUID.randomUUID().toString(), coords);
        edge.setType(Node.TYPE_SELF);
        edge.setShapeKey(NodeConstants.SHAPE_KEY_LINK_LINE);
        edge.setLineType(NodeConstants.LINE_TYPE_LINEAR);
        edge.setProperties(new HashMap<String, Object>() {{
            put("name", "连接线");
            put("type", NodeConstants.SHAPE_KEY_LINK_LINE);
        }});

        // 添加链接关系
        kg.addEdge(edge, !poleIsSource);
        kg.addEdge(segLine, poleIsSource);

        // TODO segLine的target是null 需要处理一下
        if (target != null) {
            target.addEdge(segLine, !poleIsSource);
        }
        pole.addEdge(edge, poleIsSource);
        if (target != null) {
            return Arrays.asList(pole, edge, kg, segLine, target);
        } else {
            return Arrays.asList(pole, edge, kg, segLine);
        }
    }

    /**
     * 加装柱上开关
     */
    public List<Node> generatePoleKg(List<Node> range, boolean isCopySource, Boolean switchOpen) {
        // , Boolean switchOpen
        // 如果这段区间已经有开关了  那么就不需要上了
        Node middlePole = NodeUtils.getMiddlePole(range);
        for (int i = 0; i < range.size(); i++) {
            Node node = range.get(i);
            if (node.equals(middlePole)) {
                return generatePoleKg(range.get(i), range.get(i + 1), isCopySource, switchOpen);
            }
        }
        return null;
    }

    // 转wlgt节点
    public Node toWlgtNode(String psrId, String psrType, String psrName, double[] coords) {
        Node device = nodeFactory.createDevice(psrId, coords[0], coords[1]);
        device.setType(Node.TYPE_PSR);

        device.setPsrId(device.getPsrId());
        device.setPsrType(psrType);
        device.setType(Node.TYPE_PSR);
        device.setPsrName(psrName);
        device.putProperties("name", psrName);

        return device;
    }

    //
    public Node toBdzBayKgNode(String psrId, String psrType, String psrName, double[] coords) {
        Node device = nodeFactory.createDevice(psrId, coords[0], coords[1]);
        device.setPsrType(psrType);
        device.setType(Node.TYPE_PSR);
        device.setPsrName(psrName);
        return device;
    }
}
