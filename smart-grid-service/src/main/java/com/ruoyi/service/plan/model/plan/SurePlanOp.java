package com.ruoyi.service.plan.model.plan;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.service.plan.model.lay.BaseLay;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 确定的方案操作
 */
@Data
public class SurePlanOp {
    List<PlanOperate> planOps = new ArrayList<>();

    public static SurePlanOp createSurePlanOp(List<PlanOperate> planOps) {
        SurePlanOp surePlanOp = new SurePlanOp();
        if (CollectionUtils.isNotEmpty(planOps)) {
            surePlanOp.setPlanOps(planOps);
        }
        return surePlanOp;
    }

    public void add(PlanOperate planOp) {
        planOps.add(planOp);
    }

    public void addAll(List<PlanOperate> planOps) {
        this.planOps.addAll(planOps);
    }

    /**
     * 方案的长度
     */
    public int getSize() {
        return planOps.size();
    }

    // 确定方案 按照类型和联络长度进行排序
    public void sort() {
        planOps.sort((op1, op2) -> {
            HashMap<String, Double> totalScope1 = op1.getLayTotalScope();
            HashMap<String, Double> totalScope2 = op2.getLayTotalScope();
            Double total1 = totalScope1.get("total");
            Double total2 = totalScope2.get("total");
            if (total1.doubleValue() == total2.doubleValue()) {
                return Double.compare(totalScope1.get("contactLength"), totalScope2.get("contactLength"));
            } else {
                return Double.compare(total2, total1);
            }
        });
    }

    /**
     * 转为初版方案
     */
    public List<Plan> toPlans(Long problemId, HashMap<Long, PlanOperate> planLaysMap) {
        return planOps.stream().map(planOp -> {
            Plan plan = planOp.toPlan(problemId);
            if (planLaysMap != null) {
                planLaysMap.put(plan.getId(), planOp);
            }
            plan.setLoad(planOp.toPlanLoadStr());
            plan.setN1("通过");
            plan.setPlanType(StringUtils.isNotBlank(planOp.getType()) ? planOp.getType() : "default");
            boolean isExistContactType = false;
            for (BaseLay layNode : planOp.layNodeList) {
                boolean contactType = layNode.isContactType();
                if (contactType) {
                    isExistContactType = true;
                }
            }
            if (isExistContactType) {
                plan.setEconomy("采用冗余设计，长期效益较高");
                plan.setExe("中，冗余线路建设复杂，需较多资源");
                plan.setAdvantage("高可靠性，适用关键负荷区");
                plan.setDisadvantage("投资较高，实施周期长");
            } else {
                plan.setEconomy("低成本改造，使用范围广，投资小");
                plan.setExe("低，现有线路优化施工简便");
                plan.setAdvantage("投资低，实施快速");
                plan.setDisadvantage("使用范围有限");
            }
            return plan;
        }).collect(Collectors.toList());
    }

}
