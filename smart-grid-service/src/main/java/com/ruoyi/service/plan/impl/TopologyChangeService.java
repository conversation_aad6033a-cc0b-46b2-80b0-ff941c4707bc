package com.ruoyi.service.plan.impl;

import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.map.ISingMapService;
import jodd.util.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理网架拓扑更改之后的拓扑数据
 */
@Service
public class TopologyChangeService {


    @Autowired
    ISingMapService singMapService;

    public SingAnalysis generateByProblem(String problemId) {
        // TODO 后续实现
        return null;
    }

//    /**
//     * 根据线路进行网架更改
//     *
//     * @param feederId 线路ID
//     * @param jsonStr  node的json字符串
//     */
//    public SingAnalysis generateByFeederId(String feederId, String jsonStr, boolean isZnap) {
//        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, isZnap);
//
//
//        return null;
//    }

    /**
     * 根据 NodePath获取
     *
     * @param nodePath    原nodePath
     * @param changeNodes 更改的节点集合
     * @return
     */
    public NodePath changeNodePath(NodePath nodePath, List<Node> changeNodes) {

        // 复制新的节点列表
        List<Node> nodes = NodeUtils.copyNodes(nodePath.getNodeList());
        // 处理nodeMap
        HashMap<String, Node> nodeMap = new HashMap<>();
        for (Node node : nodes) {
            if (StringUtils.isNotBlank(node.getPsrId())) {
                nodeMap.put(node.getPsrId(), node);
            }
        }

        // 处理startNode
        Node startNode = nodeMap.get(nodePath.getStartNode().getPsrId());

        // 处理联络开个
        List<Node> kgContactNodes = new ArrayList<>();
        for (Node kgContactNode : nodePath.getContactKgNodes()) {
            kgContactNodes.add(nodeMap.get(kgContactNode.getPsrId()));
        }

        // 处理contactFeederKgs
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();

        for (Node changeNode : changeNodes) {
            if (changeNode == null) {
                continue;
            }
            //  自定义设备
            if (!changeNode.isPsrNode()) {
                // 设置空的设备ID
                if (StringUtils.isBlank(changeNode.getPsrId())) {
                    changeNode.setPsrId(changeNode.getId());
                }
            } else { // 电网设备
                // 是电网设备 这里的node和基础网架里面的node实际是同一个设备 但不是同一个对象 我们需要特别处理
                Node baseNode = nodeMap.get(changeNode.getPsrId());
                if (baseNode == null) {
                    continue;
                }
                // 处理properties 合并替换
                if (baseNode.getProperties() == null) {
                    baseNode.setProperties(changeNode.getProperties());
                } else {
                    if (changeNode.getProperties() != null) {
                        baseNode.getProperties().putAll(changeNode.getProperties());
                    }
                }

                if (changeNode.isRemoveNode()) { // 当前设备移除掉 并且也处理断开连接关系
                    NodeUtils.removeNodes(Arrays.asList(baseNode), null, null);
                } else {
                    // 处理链接关系 将更改的链接关系移动到基础网架设备上的
                    handlePsrLink(changeNode, baseNode);

                    // 备用间隔新出线联络 名称更改
                    if (changeNode.getParentWhg() != null) {
                        baseNode.setPsrName("已使用");
                    }
                }
            }
        }

        // 节点路径分析
        NodePath result = new NodePath();
        result.analysisPath(startNode, kgContactNodes, contactFeederKgs);
        return result;
    }


    // =========================== 一些小方法 =======================

    /**
     * 处理链接关系 将更改的链接关系移动到基础网架设备上的
     *
     * @param changePsrNode 更改的电网节点
     * @param basePsrNode   基础网架的电网节点
     */
    private void handlePsrLink(Node changePsrNode, Node basePsrNode) {
        // 处理链接关系 将更改的链接关系移动到基础网架设备上的
        if (changePsrNode.isEdge()) {
            Node source = changePsrNode.getLink(true);
            Node target = changePsrNode.getLink(false);

            // 将关联的的source和target 重新和基础边 进行关联   之前的需要断开
            if (source != null) {
                changePsrNode.removeLink(true); // 移除之前的
                source.addEdge(basePsrNode, true); // 添加关联基础边
            }
            if (target != null) {
                changePsrNode.removeLink(false); // 移除之前的
                target.addEdge(basePsrNode, false);  // 添加关联基础边
            }
        } else {
            // 获取当前更改电网设备关联的边 并且将当前的表移到基础设备上
            List<Node> edges = changePsrNode.getEdges();
            if (CollectionUtils.isNotEmpty(edges)) {
                for (Node edge : new ArrayList<>(edges)) {
                    // 获取当前边对于对于设备是source还是target
                    Node source = edge.getLink(true);
                    boolean isSource = source != null && StringUtil.equals(source.getPsrId(), changePsrNode.getPsrId());

                    // 当前的移除
                    changePsrNode.removeEdge(edge, isSource);
                    // 基础节点新增之前更改的边
                    basePsrNode.addEdge(edge, isSource);
                }
            }
        }
    }

}
