package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.plan.AlternateOp;
import com.ruoyi.service.plan.model.plan.CombAlternateOp;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.NearDeviceInfoUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理相关联络相关单句柄
 */
@Component
public class ContactLayHandle {

    @Autowired
    PoleContactHandle poleContactHandle;

    @Autowired
    AddHwgContactHandle addHwgContactHandle;

    @Autowired
    BayContactHandle bayContactHandle;

    @Autowired
    ContactHandleService contactHandleService;

    /**
     * 不同类型联络类型对应不同的处理service
     */
    public HashMap<String, IContactHandle> getConcatHandleMap() {
        return new HashMap<String, IContactHandle>() {{
            put(ProcessContactBo.POLE_RANGE, poleContactHandle);
            put(ProcessContactBo.SEG_ADD_HWG, addHwgContactHandle);
            put(ProcessContactBo.STATION_BAY, bayContactHandle);
        }};
    }

    /**
     * 根据联络类型获取联络处理service
     */
    public IContactHandle getContactHandle(String type) {
        HashMap<String, IContactHandle> concatHandleMap = getConcatHandleMap();
        return concatHandleMap.get(type);
    }

    // =========================== 处理附近联络lay放置位置 =======================

    /**
     * 处理附近放置 联络线联络单问题
     * 处理加工联络对线
     */
    public void handleNearLayContact(Long problemId, String feederId, String token, CombAlternateOp combAlternateOp, List<NearbyDeviceInfoVo> nearDevs, List<NearbySubstationInfoVo> bdzBays) {

       // double feederLoad = contactHandleService.getFeederLoad(feederId);  // 线路负载率
      //  double maxFeederLoad = contactHandleService.getMaxFeederLoad(); // 线路最大负载率

        // =========================== 提取联络线需要加工的ProcessContactBo =======================
        List<String> meetContactTypes = Arrays.asList(PlanOperate.BDZ_NEW_LINE_TYPE, PlanOperate.CONTACT_TYPE);
        // 当前设备的所有线路ID

       // List<String> nearFeederIds = NearDeviceInfoUtil.getAllFeederIds(nearDevs);


        // TODO 这里单个处理会比较慢 看看后期能不能优化一下呢
        // 获取需要加工联络线的
        for (AlternateOp alternateOp : combAlternateOp.getAlternateOps()) {
            for (PlanOperate op : alternateOp.getAlternateOps()) {
                // 变电站新出线和联络需要处理和设置
                if (meetContactTypes.contains(op.getType())) {
                    // 当前操作不能转供的线路

//                    List<NearbyDeviceInfoVo> filterNearDevs = getTfNearDevs(feederId, nearDevs, nearFeederIds, op, feederLoad, maxFeederLoad);
//
//                    filterNearDevs = CollectionUtils.isEmpty(filterNearDevs) ? nearDevs : filterNearDevs;
                    List<NearbyDeviceInfoVo> filterNearDevs = nearDevs;
                    for (BaseLay layNode : op.getLayNodeList()) {
                        if (layNode.isContactType()) {

                            ContactLay contactLay = ((ContactLay) layNode);
                            ProcessContactBo processContactBo = contactLay.getProcessContactBo();
                            IContactHandle handle = getContactHandle(processContactBo.getType());

                            if (StringUtils.equals(op.getType(), PlanOperate.CONTACT_TYPE)) {
                                handle.handleContact(problemId, contactLay, op, filterNearDevs, token);
                            } else if (StringUtils.equals(op.getType(), PlanOperate.BDZ_NEW_LINE_TYPE)) {
                                handle.handleBdzNewLine(problemId, contactLay, op, bdzBays, token);
                            }
                        }
                    }
                }
            }
        }
    }

    List<String> getNotTfFeederIds(String feederId, List<String> nearFeederIds, PlanOperate planOperate, double currentLoad, double maxLoad) {

        HashMap<String, List<FeederTransferCap>> nearFTrsMap = new HashMap<>();
        // 不能转供的线路ID集合
        List<String> notTfFeederIds = new ArrayList<>();

        // 处理负载率
        for (BaseLay layNode : planOperate.getLayNodeList()) {
            // 通过联络开关的并且和关联的lay 会导致负载率变化
            if (layNode.isContactType()) {
                ContactLay contactLay = (ContactLay) layNode;
                if (StringUtils.isNotBlank(contactLay.getLinkLayId()) && contactLay.getContactBranch() != null) {
                    for (String nearFeederId : nearFeederIds) {
                        if (!nearFTrsMap.containsKey(nearFeederId)) {
                            nearFTrsMap.put(nearFeederId, new ArrayList<>());
                        }
                        List<FeederTransferCap> fTrs = nearFTrsMap.get(nearFeederId);
                        FeederTransferCap feederTransferCap = contactHandleService.calcFeederTransferCapByBranchNode(feederId, nearFeederId, contactLay.getContactBranch().getBranchNode());
                        fTrs.add(feederTransferCap);
                    }
                }
            }
        }

        for (String nearFeederId : nearFeederIds) {
            List<FeederTransferCap> feederTransferCaps = nearFTrsMap.get(nearFeederId);
            if (!CollectionUtils.isEmpty(feederTransferCaps)) {
                CombFeederTransfer combFTr = FindRunAdjustLay.getCombFTr(feederTransferCaps, currentLoad, maxLoad);
                // 当前线路没法转供 超载了
                if (!combFTr.isSourceHasPass() || !combFTr.isFTrHasPass()) {
                    notTfFeederIds.add(nearFeederId);
                }
            }
        }

        return notTfFeederIds;
    }

    List<NearbyDeviceInfoVo> getTfNearDevs(String feederId, List<NearbyDeviceInfoVo> nearDevs, List<String> nearFeederIds, PlanOperate planOperate, double currentLoad, double maxLoad) {
        List<String> notTfFeederIds = getNotTfFeederIds(feederId, nearFeederIds, planOperate, currentLoad, maxLoad);
        return NearDeviceInfoUtil.filterByFeederIds(nearDevs, notTfFeederIds);
    }

    /**
     * 判断当前环网柜是否可以替换
     *
     * @param hwgNode
     * @return
     */
    boolean isHwgReplace(Node hwgNode) {
        List<Node> children = hwgNode.getChildren();

        if (!CollectionUtils.isEmpty(children)) {
            // 这里需要判断当前的间隔是否大于6个 如果大于6个 那么还是需要需要新增环网柜
            List<Node> backupKgs = children.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
            if (backupKgs.size() < 6) {
                return true;
            }
        }
        return false;
    }

}
