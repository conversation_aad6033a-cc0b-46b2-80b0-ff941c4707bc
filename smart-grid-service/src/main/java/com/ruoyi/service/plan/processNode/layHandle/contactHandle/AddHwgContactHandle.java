package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.SegFeeder;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.processNode.ProcessNearNode;
import com.ruoyi.util.coordinates.CoordinateConverter;
import lombok.Data;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 新增环网柜处理
 */
@Component
public class AddHwgContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ProcessNearNode processNearNode;

    @Override
    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbyDeviceInfoVo> nearDevs, String token) {

        AddHwgBo addHwgBo = processAddHwg(lay);
        List<Node> hwgNodes = addHwgBo.getHwgNodes();
        Node nodeBay = addHwgBo.getNodeBay();
        NearNode nearNode = toNearNode(nodeBay);

        // 加工
        processNearNode.processDevRoute(nearNode, nearDevs, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = processContactHandle(nearNode, ProcessContactBo.SEG_ADD_HWG);

        processContactVo.getContactNodeList().addAll(NodeUtils.copyNodes(hwgNodes));
        lay.setProcessContactVo(processContactVo);
    }

    @Override
    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbySubstationInfoVo> bdzBays, String token) {

        AddHwgBo addHwgBo = processAddHwg(lay);
        List<Node> hwgNodes = addHwgBo.getHwgNodes();
        Node nodeBay = addHwgBo.getNodeBay();
        NearNode nearNode = toNearNode(nodeBay);

        // 加工最近的变电站
        processNearNode.processBdzRoute(nearNode, bdzBays, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = processBdzNewLineHandle(nearNode, ProcessContactBo.SEG_ADD_HWG);

        processContactVo.getContactNodeList().addAll(NodeUtils.copyNodes(hwgNodes));
        lay.setProcessContactVo(processContactVo);
    }

    @Data
    private class AddHwgBo {
        public AddHwgBo(Node nodeBay, List<Node> hwgNodes) {
            this.nodeBay = nodeBay;
            this.hwgNodes = hwgNodes;
        }

        Node nodeBay;

        List<Node> hwgNodes;
    }

    private AddHwgBo processAddHwg(ContactLay lay) {

        ProcessContactBo processContact = lay.getProcessContactBo();
        SegBreakNodeBo segBreakNode = processContact.getSegBreakNode();

        // 处理放置的中线坐标点
        segBreakNodePoint(segBreakNode);

        // ===========================  =======================

        // 环网柜节点结合集合
        List<Node> hwgNodes = breakSegAddHwg(segBreakNode);

        // 没有使用的间隔
        List<Node> nodeNoUses = hwgNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
        Node nodeBay = nodeNoUses.get(0);

        return new AddHwgBo(nodeBay, hwgNodes);
    }


    /**
     * 加工导线段打断的中心坐标
     */
    public void segBreakNodePoint(SegBreakNodeBo segBreakNode) {
        Node segEdge = segBreakNode.getSegEdge();

        //1、使用开始和结束
        List<Double> startPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getStartNode().getPsrId(), segBreakNode.getStartNode().getPsrType());
        List<Double> endPoint = queryDeviceInfo.selectDeviceCoords(segBreakNode.getEndNode().getPsrId(), segBreakNode.getEndNode().getPsrType());

        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(startPoint) || com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(endPoint)) {
            SegFeeder segFeeder = segFeederMapper.selectSegFeeder(segEdge.getPsrId());
            if (segFeeder != null && StringUtils.isNotBlank(segFeeder.getCoordinate())) {
                List<List<Double>> coords = CoordinateConverter.parseCoordinates(segFeeder.getCoordinate());
                startPoint = coords.get(0);
                endPoint = coords.get(coords.size() - 1);
            }
        }
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(startPoint) && com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(endPoint)) {
            LineString lineString = CoordinateConverter.toLineString(Arrays.asList(startPoint, endPoint));
            Point centroid = lineString.getCentroid();

            segBreakNode.setStartPoint(startPoint);
            segBreakNode.setEndPoint(endPoint);
            segBreakNode.setPoint(centroid);
        }
    }

}
