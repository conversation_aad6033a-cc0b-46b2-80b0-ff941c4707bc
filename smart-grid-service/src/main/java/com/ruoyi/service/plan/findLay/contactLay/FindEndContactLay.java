package com.ruoyi.service.plan.findLay.contactLay;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.ToNodeNumsFunc;
import com.ruoyi.service.plan.findLay.contactLay.utils.LayPositionUtil;
import com.ruoyi.service.plan.findLay.contactLay.utils.NumModelUtil;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.findLay.NodeNumModel;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用于查找需要放置分支末端联络位置工具类
 * <p>
 * 查找逻辑：
 * 1、从开始节点逐层往里查找遍历
 * 2、然后判断各个分叉的指标 是否符合
 * <p>
 * 注意点：
 * 一、放置的位置限制：
 * 1、环网柜/开关站里的剩余间隔开关（如果当前站房已经有其他联络线了 那么也不能，没有一样）
 * 2、杆塔上
 */
@Service
public class FindEndContactLay {

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    ContactHandleService contactHandleService;

    /**
     * 获取大分子无联络的
     *
     * @param startNode 开始节点
     * @param nextEdge  下一个边
     * @param minPbNum  最小配变数量
     * @param minCap    最小容量数量
     * @param nodePath
     * @return
     */
    public List<ContactBranch> getBigBranch(Node startNode, Node nextEdge, Integer minPbNum, Integer minCap, NodePath nodePath) {
        // 查询所有满足的组合 重分组里面取其中一个即可
        List<List<ContactBranch>> groupBigBranch = getAllMeetLays(
                startNode,
                nextEdge,
                nodePath.getBranchAllNodeMap(),
                new HashMap<>(),
                NumModelUtil::toCapNum,
                (nodeNumModel) -> NumModelUtil.judgeBigBranch(nodeNumModel, minPbNum, minCap)
        );
        return getFirstBranch(groupBigBranch);
    }

    /**
     * 获取分段配变不合理的大分子的
     *
     * @param startNode 开始节点
     * @param nextEdge  下一个边
     * @param maxNum    最小配变数量
     * @param nodePath
     * @return
     */
    public List<ContactBranch> getPbNumBranch(Node startNode, Node nextEdge, Integer maxNum, NodePath nodePath) {
        // 查询所有满足的组合 重分组里面取其中一个即可
        List<List<ContactBranch>> groupBigBranch = getAllMeetLays(
                startNode,
                nextEdge,
                nodePath.getBranchAllNodeMap(),
                new HashMap<>(),
                NumModelUtil::toPbNums,
                (nodeNumModel) -> NumModelUtil.judgePbNumModel(nodeNumModel, maxNum)
        );
        return getFirstBranch(groupBigBranch);
    }

    /**
     * 获取线路重载的
     *
     * @param startNode    开始节点
     * @param nextEdge     下一个边
     * @param mainFeederNt 当前主问题线路nt
     * @param maxLoad      最大负载率
     * @param nodePath
     * @return
     */
    public List<ContactBranch> getFeederLoad(Node startNode, Node nextEdge, FeederNtVo mainFeederNt, double maxLoad, NodePath nodePath) {
        return getFeederLoad(startNode, nextEdge, mainFeederNt, maxLoad, nodePath, null);
    }

    /**
     * 获取线路重载的
     *
     * @param startNode    开始节点
     * @param nextEdge     下一个边
     * @param mainFeederNt 当前主问题线路nt
     * @param maxLoad      最大负载率
     * @param nodePath
     * @return
     */
    public List<ContactBranch> getFeederLoad(
            Node startNode,
            Node nextEdge,
            FeederNtVo mainFeederNt,
            double maxLoad,
            NodePath nodePath,
            BiFunction<List<Node>, Double, Boolean> judgeFunc
    ) {
        // 查询所有满足的组合 重分组里面取其中一个即可  注意一个即可
        List<List<ContactBranch>> groupBigBranch = getAllMeetLays(startNode,
                nextEdge,
                nodePath.getBranchAllNodeMap(),
                new HashMap<>(),
                NumModelUtil::toCapNum,
                (nodeNumModel) -> NumModelUtil.judgeFeederLoad(nodeNumModel, mainFeederNt, maxLoad, judgeFunc));

        // 这里最终提取查找一个确定好的联络分支
        return getLastBranch(groupBigBranch, (List<ContactBranch> lays) -> {
            if (lays.size() >= 2) {
                ProcessContactBo pContactByContactLay1 = LayPositionUtil.getPContactByContactLay(lays.get(0), nodePath).get(0);
                // 如果新增环网柜 我们尽可能完后靠
                if (pContactByContactLay1 != null
                        && (StringUtils.equals(pContactByContactLay1.getType(), ProcessContactBo.SEG_ADD_HWG)
                        || StringUtils.equals(pContactByContactLay1.getType(), ProcessContactBo.HWG_REPLACE))
                ) {
                    ProcessContactBo pContactByContactLay2 = LayPositionUtil.getPContactByContactLay(lays.get(1), nodePath).get(0);
                    if (pContactByContactLay2 != null && !(StringUtils.equals(pContactByContactLay2.getType(), ProcessContactBo.SEG_ADD_HWG) || StringUtils.equals(pContactByContactLay2.getType(), ProcessContactBo.HWG_REPLACE))) {
                        return lays.get(1);
                    }
                }
            }
            return lays.get(0);
        });
    }

    /**
     * 获取最开始分支的
     *
     * @param groupLays
     * @return
     */
    public List<ContactBranch> getFirstBranch(List<List<ContactBranch>> groupLays) {
        List<ContactBranch> result = new ArrayList<>();
        for (List<ContactBranch> lays : groupLays) {
            lays.sort(Comparator.comparingInt(d -> d.getPaths().size()));
            result.add(lays.get(0));
        }
        updateExtraNode(result);
        return result;
    }

    /**
     * 获取末尾分支点
     *
     * @param groupLays
     * @return
     */
    public List<ContactBranch> getLastBranch(List<List<ContactBranch>> groupLays, Function<List<ContactBranch>, ContactBranch> applyLay) {
        List<ContactBranch> result = new ArrayList<>();
        for (List<ContactBranch> lays : groupLays) {
            lays.sort((d1, d2) -> d2.getPaths().size() - d1.getPaths().size());
            if (applyLay != null) {
                result.add(applyLay.apply(lays));
            } else {
                result.add(lays.get(0));
            }
        }
        updateExtraNode(result);
        return result;
    }

    /**
     * 更新排除分支节点
     */
    public void updateExtraNode(List<ContactBranch> lays) {
        if (CollectionUtils.isNotEmpty(lays) && lays.size() > 1) {

            for (ContactBranch contactBranch : lays) {
                // 其他
                List<ContactBranch> others = lays.stream().filter(d -> d != contactBranch).collect(Collectors.toList());
                Map<String, List<BranchNode>> branchNodeMap = new HashMap<>();
                for (BranchNode branchNode : others.stream().map(ContactBranch::getBranchNode).collect(Collectors.toList())) {
                    if (!branchNodeMap.containsKey(branchNode.getPsrId())) {
                        branchNodeMap.put(branchNode.getPsrId(), new ArrayList<>());
                    }
                    branchNodeMap.get(branchNode.getPsrId()).add(branchNode);
                }

                // 判断当前大分子里  存在其他的分支 那么就添加进去
                BranchNode branchNode = contactBranch.getBranchNode();
                for (Node node : branchNode.getNodes()) {
                    List<BranchNode> branchNodes = branchNodeMap.get(node.getPsrId());
                    if (CollectionUtils.isNotEmpty(branchNodes)) {
                        contactBranch.getExtraBranchNodes().addAll(branchNodes);
                    }
                }

            }
        }
    }

    /**
     * 获取所以满足的组合
     *
     * @param startNode
     * @param nextEdge
     * @param branchNodeMap
     * @param useNodeMap
     * @param toNodeNums
     * @param judgeNodeNum
     * @return
     */
    private static List<List<ContactBranch>> getAllMeetLays(Node startNode,
                                                            Node nextEdge,
                                                            HashMap<String, BranchNode> branchNodeMap,
                                                            Map<String, Boolean> useNodeMap,
                                                            ToNodeNumsFunc<List<Node>, Node, HashMap<String, BranchNode>, List<NodeNumModel>> toNodeNums,
                                                            Function<NodeNumModel, Boolean> judgeNodeNum) {
        List<List<ContactBranch>> result = new ArrayList<>();
        loopBranchPaths(startNode,
                nextEdge,
                result,
                branchNodeMap,
                useNodeMap,
                true,
                toNodeNums,
                judgeNodeNum);
        return result;
    }

    /**
     * 由于一个分支下由无数分支构成 获取满足规则的所有主分支
     * 实现逻辑：从开始节点逐步往里遍历此时就当成主分支，
     * 如果遇到分叉路 如果分叉路有超过两个是满足条件的 那么此时一条还是主 另一条重新开始的主分支
     */
    private static void loopBranchPaths(
            Node startNode,
            Node nextEdge,
            List<List<ContactBranch>> result,
            HashMap<String, BranchNode> branchNodeMap,
            Map<String, Boolean> useNodeMap,
            boolean isInit,
            ToNodeNumsFunc<List<Node>, Node, HashMap<String, BranchNode>, List<NodeNumModel>> toNodeNums,
            Function<NodeNumModel, Boolean> judgeNodeNum) {

        List<Node> paths = new ArrayList<>();
        List<ContactBranch> groupContLays = new ArrayList<>();
        //  List<BranchNode> extraNodes = new ArrayList<>();

        Node currentNode = startNode;
        while (currentNode != null) {
            // 递归获取查找  // 新增当前的线路
            useNodeMap.put(currentNode.getId(), true);
            paths.add(currentNode);
            Node nextNode = null;
            List<Node> edges = null;
            // 中压用户接入点 直接结束
            if (currentNode.isUserPoint()) {
                break;
            }
            if (currentNode.isEdge()) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = source == null || useNodeMap.containsKey(source.getId()) ? target : source;

                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤仅仅需要的下一个边
                // 起始边
                if (nextEdge != null) {
                    edges = NodeUtils.filterNextEdges(edges, currentNode, nextEdge);
                    nextEdge = null;
                }

                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());

                if (!isInit && edges.size() == 1) {
                    nextNode = edges.get(0);
                } else {
                    // =========================== 分叉路进行判断 =======================
                    // 自定义当前分叉点模型统计
                    List<NodeNumModel> nodeNums = toNodeNums.apply(edges, currentNode, branchNodeMap);
                    if (CollectionUtils.isNotEmpty(nodeNums)) {
                        boolean isNextMain = false;
                        // 当前的分叉点遍历下一个 进行判断是否满足
                        for (NodeNumModel nodeNum : nodeNums) {
                            // 如果满足
                            if (judgeNodeNum != null && judgeNodeNum.apply(nodeNum)) {
                                if (isNextMain) {
                                    // 其它有问题超过数量的分支线
                                    loopBranchPaths(currentNode, nodeNum.getNextNode(), result, branchNodeMap, useNodeMap, true, toNodeNums, judgeNodeNum);
                                    continue;
                                }
                                isNextMain = true;

                                groupContLays.add(new ContactBranch(new ArrayList<>(paths), nodeNum.getNode(), nodeNum.getBranchNode()));
                            }

                            if (nextNode == null) {
                                nextNode = nodeNum.getNextNode();
                            } else {
//                            BranchNode branchNode = nodeNum.getBranchNode();
//                            if (branchNode != null) {
//                                extraNodes.add(branchNode);
//                            }
                            }
                        }
                    } else {
                        if (isInit && edges.size() == 1) {
                            nextNode = edges.get(0);
                        }
                    }

                    isInit = false;
                }
            }

            currentNode = nextNode;
        }

        if (CollectionUtils.isNotEmpty(groupContLays)) {
            result.add(groupContLays);
        }
    }
}
