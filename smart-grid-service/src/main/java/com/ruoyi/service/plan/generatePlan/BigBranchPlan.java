package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 大分子无联络线网架方案生成
 * 解决措施：分支首位置添加一个首开开关，在分支末端添加联络线
 * （那条线都可以，但是优先考虑240线径的，240可以做主干路径）
 * （为什么不推荐中间呢：减少环网中的环流）
 */
@Service
@Slf4j
public class BigBranchPlan implements IGeneratePlan {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ISingMapService singMapService;


    @Autowired
    IConditionService iConditionService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {

        // 在结尾末端
        planProcessService.pushLoadingProcess(problemId, "解析网架拓扑结构中");

        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();

        // znap拓扑节点构建
        Map<String, Node> nodeMap = nodePath.getNodeMap();
        Map<String, Node> nodeIdMap = nodePath.getNodeIdMap();

        planProcessService.pushLoadingProcess(problemId, "问题结构化中");

        // 获取当前方案对应的分支线
        BranchNode branchNode = segBranchService.getMainBranch(nodePath, deviceId);
        if (branchNode == null) {
            throw new RuntimeException("暂未获取当前的大分支分段，请确定数据正确性！");
        }

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, branchNode.getPbNodes());

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, branchNode.getPbNodes());

        Integer minPbNum = iConditionService.bigBranchSegmentation();

        Integer minCap = iConditionService.bigBranchCapacityNum();


        // (3)、推送：问题释义
        pushPlanProcessService.pushBigExplain(problemId, minPbNum, minCap);

        // 确定是 配变过多 还是 容量过大 或者 负荷 TODO 暂不考虑负荷过多
        List<Node> branchNodes = branchNode.getNodes();

        // (4)、推送：大分子识别
        pushPlanProcessService.pushBigBranchIdentify(problemId, branchNode);

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(branchNodes);

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // 大分支开始节点
        Node startBranchNode = nodeMap.get(branchNode.getPsrId());
        Node nextBranchNode = nodeIdMap.get(branchNode.getNextId());

        List<ContactBranch> bigBranch = findEndContactLay.getBigBranch(startBranchNode, nextBranchNode, minPbNum, minCap, nodePath);

        CombAlternateOp combAlternateOp = ContactLayPlanOp.getBigBranchCombAltOp(bigBranch, nodePath);

        // 给杆塔加装坐标
        baseGeneratePlan.layPositionCoords(combAlternateOp);

        SurePlanOp surePlanOp = null;
        try {
            surePlanOp = processNodeService.handleLayOperateNode(problemId, combAlternateOp, token, feederId, nodePath);
        } catch (Exception e) {
            log.error("生产方案异常！", e);
            throw new RuntimeException("生产方案异常！", e);
        }

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (8)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, plans, planLaysMap);

        // 获取各个分支末端
        return plans;
    }
}
