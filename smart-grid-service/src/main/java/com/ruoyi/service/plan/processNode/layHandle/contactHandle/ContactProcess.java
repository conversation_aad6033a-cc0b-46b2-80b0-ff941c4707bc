package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.NodeConstants;
import com.ruoyi.entity.device.DevInfo;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.StationConfiguration;
import com.ruoyi.entity.map.ClosestNode;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.NeedFeederVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.utils.StationNodeUtils;
import com.ruoyi.mapper.device.SegFeederMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.service.plan.model.NearFeedersBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.service.plan.processNode.layHandle.ProcessUtils;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.NearDeviceInfoUtil;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.vo.BusbarSwitchVo;
import org.locationtech.jts.geom.LineString;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.constant.NodeConstants.LINE_TYPE_LINEAR;
import static com.ruoyi.constant.NodeConstants.SHAPE_KEY_FEEDER_DL;
import static com.ruoyi.graph.Node.TYPE_SELF;

/**
 * 联络线加工类
 */
@Component
public class ContactProcess {

    @Autowired
    MapServiceImpl mapService;

    @Autowired
    NodeFactory nodeFactory;

    @Autowired
    ProcessUtils processUtils;

    @Autowired
    SegFeederMapper segFeederMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    /**
     * 获取附近线路
     */
    NearFeedersBo getNearFeeders(List<double[]> coords, List<Node> nodes, List<DeviceFeeder> feederList) {
        // 所以附近的线路集合（每个坐标对应的线路集合）
        List<List<NeedFeederVo>> coordsToNears = mapService.pointByParallel(coords, -1, feederList);
        coordsToNears = coordsToNears.stream().filter(Objects::nonNull).collect(Collectors.toList());

        // 当前所有的附近线路集合
        List<NeedFeederVo> nearFeeders = new ArrayList<>();
        for (List<NeedFeederVo> list : coordsToNears) {
            nearFeeders.addAll(list);
        }
        // 去重
        nearFeeders = ListUtils.distinctByKey(nearFeeders, NeedFeederVo::getPsrId);
        NearFeedersBo nearFeedersBo = new NearFeedersBo(nodes, coordsToNears, nearFeeders);

        HashMap<String, List<NeedFeederVo>> nodeToNears = new HashMap<>();
        // 将设备集合和坐标一一对应
        for (int i = 0; i < nodes.size(); i++) {
            List<NeedFeederVo> needFeederVos = coordsToNears.get(i);
            nodeToNears.put(nodes.get(i).getPsrId(), needFeederVos);
        }
        nearFeedersBo.setNodeToNears(nodeToNears);

        return nearFeedersBo;
    }

    /**
     * 加工处理联络
     *
     * @param constantType 联络类型
     */
    ProcessContactVo processContactHandle(NearNode nearNode, String constantType) {

        // 每个节点都单独处理
        ClosestNode closestNode = nearNode.getClosestNode();

        // 根据开始和结束节点 调用产生路由
        ProcessContactVo processContactVo = processContactRoute(nearNode, closestNode, constantType);

        return processContactVo;
    }

    /**
     * 加工处理遍变电站新出线
     *
     * @param constantType 联络类型
     */
    ProcessContactVo processBdzNewLineHandle(NearNode nearNode, String constantType) {
        // 每个节点都单独处理
        // 根据开始和结束节点 调用产生路由
        // TODO busKg
        ProcessContactVo processContactVo = processBdzNewLineRoute(nearNode, constantType);
        return processContactVo;
    }

    /**
     * 加工联络路由
     */
    ProcessContactVo processContactRoute(NearNode nearNode, ClosestNode closestNode, String constantType) {
        Node node = nearNode.getNode();

        // 路由路径节点集合
        List<Node> nodeRouteNodeList = closestNode.getRouteNodeList();
        ProcessContactVo result = new ProcessContactVo();

        NearbyDeviceInfoVo nearDev = (NearbyDeviceInfoVo) closestNode.getClosest();
        DevInfo contactDevInfo = NearDeviceInfoUtil.getContactDevInfo(nearDev);

        // 联络线路径节点加工
        List<Node> newPathNodes = processContactPaths(nodeRouteNodeList, node, constantType, contactDevInfo);

        // 生产加工路径对象
        result.setTotalLength(closestNode.getLength());
        result.setContactNodeList(newPathNodes);
        result.setStartPsrId(node.getPsrId());
        result.setEndPsrId(contactDevInfo.getPsrId());

        String[] feeder = NearDeviceInfoUtil.getFeeder(nearDev, contactDevInfo);
        result.setContactFeederId(feeder[0]);
        result.setContactFeederName(feeder[1]);
        result.setContactNode(node);

        return result;
    }

    /**
     * 加工变电站新出线路由
     */
    ProcessContactVo processBdzNewLineRoute(NearNode nearNode, String constantType) {
        Node node = nearNode.getNode();

        ClosestNode closestNode = nearNode.getClosestNode();
        NearbySubstationInfoVo nearBdz = (NearbySubstationInfoVo) closestNode.getClosest();
        BusbarSwitchVo busKg = nearBdz.getCanBay();

        // 路由路径节点集合
        List<Node> nodeRouteNodeList = closestNode.getRouteNodeList();
        ProcessContactVo processContactVo = new ProcessContactVo();

        // 联络线路径节点加工
        List<Node> newPathNodes = processBdzNewLine(nodeRouteNodeList, node, constantType, busKg);

        // 生产加工路径对象
        processContactVo.setTotalLength(closestNode.getLength());
        processContactVo.setContactNodeList(newPathNodes);
        processContactVo.setStartPsrId(node.getPsrId());

        processContactVo.setBdzId(busKg.getStationPsrId());
        processContactVo.setBdzName(busKg.getStationPsrName());
        processContactVo.setBdzBusId(busKg.getBusbarId());
        processContactVo.setBdzBusName(busKg.getBusbarName());
        processContactVo.setBdzBayKgId(busKg.getSwitchId());
        processContactVo.setBdzBayKgName(busKg.getSwitchName());
        processContactVo.setContactNode(node);

        return processContactVo;
    }

    List<Node> processContactNodes(List<Node> nodes, Node startNode, String constantType, Function<Node, Void> handleLinkNode) {
        if (nodes == null) {
            return null;
        }

        ArrayList<Node> result = new ArrayList<>(nodes);
        // 移除路由路径首节点
        result.remove(0);
        Node startEdge = result.get(0);

        Node linkNode = null;

        // 1、处理首部
        if (StringUtils.equals(constantType, ProcessContactBo.POLE_RANGE)) {
            // 处理首节点新增联络开关
            List<Node> startNodes = processUtils.generatePoleKg(startNode, startEdge, true, null);
            List<Node> kgs = null;

            // 截取到开关的位置 并且将开始与之连接
            for (int i = startNodes.size() - 1; i >= 0; i--) {
                Node node = startNodes.get(i);
                if (node.isKg("all")) {
                    linkNode = node;
                    kgs = startNodes.subList(0, i + 1);
                    break;
                }
            }
            result.addAll(0, kgs);
        } else if (StringUtils.equals(constantType, ProcessContactBo.STATION_BAY)) {
            // 剩余间隔的开始节点 我们需要重新设置
            linkNode = startNode.clone();
            result.add(0, linkNode);
            // 与线路联络线建立连接关联关系
            linkNode.addEdge(startEdge, true);
        } else {
            linkNode = startNode;
            // 与线路联络线建立连接关联关系
            linkNode.addEdge(startEdge, true);
        }

        handleLinkNode.apply(linkNode);

        return result;
    }

    /**
     * 加工联络线的路径节点集合
     *
     * @param nodes          路由路径集合节点
     * @param constantType   联络类型 参考ProcessContactBo里面的type
     * @param startNode      结束杆塔节点
     * @param contactDevInfo
     */
    List<Node> processContactPaths(List<Node> nodes, Node startNode, String constantType, DevInfo contactDevInfo) {

        List<Node> result = processContactNodes(nodes, startNode, constantType, (linkNode) -> {
            linkNode.setContactFeeder(contactDevInfo.getFeederId(), contactDevInfo.getFeederName());
            return null;
        });

        // 2、处理末尾
        if (nodes.size() > 2) {
            // 结束设备可能是杆塔也可能是剩余的间隔
            // TODO 处理结束的设备
            Node endPole = processUtils.toWlgtNode(contactDevInfo.getPsrId(), contactDevInfo.getPsrType(), contactDevInfo.getPsrType(), contactDevInfo.getLngLat());
            result.remove(result.size() - 1);
            Node edge = result.get(result.size() - 1);
            endPole.addEdge(edge, false);
            result.add(endPole);
        }
        return result;
    }

    /**
     * 加工联络线的路径节点集合
     *
     * @param nodes        路由路径集合节点
     * @param constantType 联络类型 参考ProcessContactBo里面的type
     * @param startNode    结束杆塔节点
     * @param
     */
    List<Node> processBdzNewLine(List<Node> nodes, Node startNode, String constantType, BusbarSwitchVo busKg) {

        List<Node> result = processContactNodes(nodes, startNode, constantType, (linkNode) -> {
            linkNode.setBdzNewLine(busKg);
            return null;
        });

        // 2、处理末尾
        if (nodes.size() > 2) {
            // 表示从杆塔处新建联络线
            // TODO 缺少类型
            Node endNoe = processUtils.toBdzBayKgNode(busKg.getSwitchId(), "0305", busKg.getSwitchName(), busKg.getLngLat());
            result.remove(result.size() - 1);
            Node edge = result.get(result.size() - 1);
            endNoe.addEdge(edge, false);
            result.add(endNoe);
        }
        return result;
    }

    /**
     * 导线段在中间打断 并且在中间上新的环网柜
     *
     * @param segBreakNode 导线段对线
     */
    List<Node> breakSegAddHwg(SegBreakNodeBo segBreakNode) {

        // 设备类型zf07 获取站房以及以下的node
        List<Double> coordinateList = segBreakNode.toLngLat();
        List<Node> stationNodes = StationNodeUtils.generateStationAndChildren("环网柜", "zf07", coordinateList, 6, new StationConfiguration());

        Node startNode = segBreakNode.getStartNode().clone();
        Node endNode = segBreakNode.getEndNode().clone();
        Node segEdge = segBreakNode.getSegEdge().clone();

        // =========================== 创建两边导线段 =======================

        List<Node> nodeNoKgs = stationNodes.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());
        Node firstBayNode = nodeNoKgs.get(0);
        Node lastBayNode = nodeNoKgs.get(nodeNoKgs.size() - 1);

        LineString firstLineString = CoordinateConverter.toLineString(Arrays.asList(segBreakNode.getStartPoint(), CoordinateConverter.pointToLngLat(firstBayNode.getGeometry().getInteriorPoint())));

        LineString endLineString = CoordinateConverter.toLineString(Arrays.asList(segBreakNode.getEndPoint(), CoordinateConverter.pointToLngLat(lastBayNode.getGeometry().getInteriorPoint())));

        String shapeKey = StringUtils.equals(segEdge.getPsrType(), "dxd") ? NodeConstants.SHAPE_KEY_FEEDER_JK : NodeConstants.SHAPE_KEY_FEEDER_DL;

        Node startEdge = segEdge.clone();
        startEdge.setPsrName("起始导线段");
        startEdge.setEdge(true);
        startEdge.setId(UUID.randomUUID().toString());
        startEdge.setPsrId(null);

        startEdge.setLineType(LINE_TYPE_LINEAR);
        startEdge.setShapeKey(shapeKey);
        startEdge.setType(TYPE_SELF);
        startEdge.setGeometry(firstLineString);
        startEdge.putProperties("type", shapeKey);

        Node endEdge = segEdge.clone();
        endEdge.setPsrName("结束导线段");
        endEdge.setEdge(true);
        endEdge.setId(UUID.randomUUID().toString());
        startEdge.setPsrId(null);
        endEdge.setLineType(LINE_TYPE_LINEAR);
        endEdge.setShapeKey(SHAPE_KEY_FEEDER_DL);
        endEdge.setType(TYPE_SELF);
        endEdge.setGeometry(endLineString);
        endEdge.putProperties("type", shapeKey);

        // =========================== 建立链接关系 =======================
        // TODO 并且将开关的名称改为”已使用“（后面有空再改为”有XXXX供，至XXXXXX变“）

        //增加新增的两条线的sourcePort和targetPort以及间隔的Port中的第二个point
        //新增起始点和起始导线段的关系
        startNode.addEdge(startEdge, true);
        //新增第一个间隔和起始导线段的关系
        firstBayNode.addEdge(startEdge, false);
        firstBayNode.setPsrName("已使用");
        //新增第二个间隔和结束导线段的关系
        endNode.addEdge(endEdge, false);
        //结束点和起始结束导线段
        lastBayNode.addEdge(endEdge, true);
        lastBayNode.setPsrName("已使用");

        //将修改的间隔node进行替换
        stationNodes.add(startEdge);
        stationNodes.add(endEdge);
        stationNodes.add(startNode);
        stationNodes.add(endNode);

        //删除中间导线段,新增导线段的properties属性"remove", "true"
        segEdge.setRemoveNode();
        stationNodes.add(segEdge);

        //新增起始导线段的targetPort
//        PortItem targetPortItem = (PortItem)firstNode .getPorts().getItems().get(0);
//        startEdge.setTargetPort(targetPortItem.getId());

        //新增结束导线段的sourcePort
//        PortItem sourcePortItem = (PortItem)lastNode .getPorts().getItems().get(0);
//        startEdge.setSourcePort(sourcePortItem.getId());
        //修改起始间隔和结束间隔的第二个point，顺便把备用改成已使用

        for (Node node : stationNodes) {
            if (StringUtils.isBlank(node.getPsrId())) {
                node.setPsrId(node.getId());
            }
        }

        return stationNodes;
    }

    /**
     * 根据联络线进行分组
     */
    HashMap<String, List<ProcessContactVo>> prosContactGroup(List<ProcessContactVo> prosContacts) {

        HashMap<String, List<ProcessContactVo>> group = new HashMap<>();
        for (ProcessContactVo processContact : prosContacts) {

            String contactFeederId = processContact.getContactFeederId();
            List<ProcessContactVo> list = new ArrayList<>();

            if (group.containsKey(contactFeederId)) {
                list = group.get(contactFeederId);
            } else {
                group.put(contactFeederId, list);
            }
            list.add(processContact);
        }
        return group;
    }

    /**
     * 提取联络线
     * 不同联络线的取一个即可 并且取最短那个杆塔即可
     *
     * @param allContacts
     * @return
     */
    List<ProcessContactVo> extraProsContact(List<ProcessContactVo> allContacts) {
        HashMap<String, List<ProcessContactVo>> contactGroup = prosContactGroup(allContacts);
        List<ProcessContactVo> result = new ArrayList<>();
        // 只需提取联络线里面的某个即可
        for (List<ProcessContactVo> prosContacts : contactGroup.values()) {
            // 排序 取最短的长度
            prosContacts.sort((pContact1, pContact2) -> {
                double length1 = pContact1.getTotalLength() == null ? 0.0 : pContact1.getTotalLength();
                double length2 = pContact2.getTotalLength() == null ? 0.0 : pContact2.getTotalLength();
                return Double.compare(length1, length2);
            });
            result.add(prosContacts.get(0));
        }
        return result;
    }

}
