package com.ruoyi.service.plan.utils;

import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.lay.*;
import com.ruoyi.util.ListUtils;

import java.util.Arrays;
import java.util.List;

/**
 * layNode加工处理
 */
public class LayNodeFactory {

    // =========================== 开关相关 =======================

    /**
     * 首开关
     *
     * @param headKgPaths 放在第范围节点路径集合
     * @param switchOpen  开关状态
     */
    public static BaseLay createHeadKg(List<Node> headKgPaths, Boolean switchOpen) {
        // 获取当前出现开关的首位置下标
        Node firstKg = ListUtils.findFirst(headKgPaths, n -> n.isKg("all"));
        if (firstKg == null) {
            return LayNodeFactory.createHeadKg(headKgPaths.get(0), headKgPaths.get(1), switchOpen);
        } else {
            // 如果这段区间已经有开关了  那么就不需要上新了
            return LayNodeFactory.createHeadKg(firstKg, switchOpen);
        }
    }

    /**
     * 开关
     *
     * @param psrKg      电网设备的开关
     * @param switchOpen 开关状态
     */
    public static BaseLay createHeadKg(Node psrKg, Boolean switchOpen) {
        return new KgLay(BaseLay.KG_HEAD_TYPE, psrKg, switchOpen);
    }

    /**
     * 开关
     *
     * @param beforeNode 开始节点
     * @param afterNode
     * @param switchOpen 开关状态
     */
    public static BaseLay createHeadKg(Node beforeNode, Node afterNode, Boolean switchOpen) {
        return new KgLay(BaseLay.KG_HEAD_TYPE, beforeNode, afterNode, switchOpen);
    }

    /**
     * 开关
     *
     * @param psrKg      电网设备的开关
     * @param switchOpen 开关状态
     */
    public static BaseLay createKg(Node psrKg, Boolean switchOpen) {
        return new KgLay(BaseLay.KG_TYPE, psrKg, switchOpen);
    }

    /**
     * 开关
     *
     * @param beforeNode 开始节点
     * @param afterNode
     * @param switchOpen 开关状态
     */
    public static BaseLay createKg(Node beforeNode, Node afterNode, Boolean switchOpen) {
        return new KgLay(BaseLay.KG_TYPE, beforeNode, afterNode, switchOpen);
    }

    // =========================== 运放调整 =======================

    /**
     * 创建运方调整的lay
     */
    public static BaseLay createRunAdjust(FeederTransferCap feederTransferCap) {
        return new RunAdjustLay(Arrays.asList(feederTransferCap));
    }

    /**
     * 创建运方调整的lay
     */
    public static BaseLay createRunAdjust(List<FeederTransferCap> fTrs) {
        return new RunAdjustLay(fTrs);
    }

    // =========================== 联络相关 =======================

    /**
     * 末端联络线
     *
     * @param processContactBo 加工联络线bo
     * @param linkLayId        当前末端关联其它lay的ID（用于关联绑定一起）
     * @return
     */
    public static BaseLay createEndContact(ProcessContactBo processContactBo, String linkLayId, ContactBranch contactBranch) {
        ContactLay layNode = new ContactLay(BaseLay.END_CONTACT_TYPE, processContactBo);
        layNode.setLinkLayId(linkLayId);
        layNode.setContactBranch(contactBranch);
        return layNode;
    }

    // =========================== 间隔相关 =======================

    /**
     * 替换间隔
     *
     * @param replaceBayNodes 替换间隔节点集合
     */
    public static BaseLay createReplaceBay(List<Node> replaceBayNodes) {
        return new ReplaceBayLay(replaceBayNodes);
    }

}
