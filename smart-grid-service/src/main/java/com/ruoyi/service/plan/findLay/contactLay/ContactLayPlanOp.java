package com.ruoyi.service.plan.findLay.contactLay;

import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.findLay.contactLay.utils.LayPositionUtil;
import com.ruoyi.service.plan.model.HwgBayBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.AlternateOp;
import com.ruoyi.service.plan.model.plan.CombAlternateOp;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.utils.LayNodeFactory;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.function.BiFunction;

public class ContactLayPlanOp {
    /**
     * 获取末端放置联络线的所有集合
     */
    public static CombAlternateOp getBigBranchCombAltOp(List<ContactBranch> contactBranches, NodePath nodePath) {

        CombAlternateOp combAlternateOp = new CombAlternateOp();

        // 一条分支产生一个备选操作
        for (ContactBranch contactBranch : contactBranches) {
            // 所有的分支线
            List<PlanOperate> planOps = getEndContactPlanOps(contactBranch, nodePath);

            combAlternateOp.add(new AlternateOp(planOps));
        }

        return combAlternateOp;
    }

    /**
     * 获取末端放置联络线的所有备选集合
     */
    public static CombAlternateOp getEndContactCombAltOpByBranches(List<ContactBranch> contactBranches, NodePath nodePath) {

        CombAlternateOp combAlternateOp = new CombAlternateOp();
        AlternateOp alternateOp = new AlternateOp();
        // 只有一个备选
        for (ContactBranch contactBranch : contactBranches) {
            List<PlanOperate> planOps = getEndContactPlanOps(contactBranch, nodePath);
            alternateOp.addAll(planOps);
        }
        combAlternateOp.add(alternateOp);
        return combAlternateOp;
    }

    /**
     * 获取末端放置联络线的所有备选集合
     */
    public static CombAlternateOp getEndContactCombAltOpByPaths(List<ArrayList<Node>> allNodePaths, NodePath nodePath) {

        CombAlternateOp combAlternateOp = new CombAlternateOp();
        AlternateOp alternateOp = new AlternateOp();

        // 只有一个备选
        for (List<Node> paths : allNodePaths) {
            PlanOperate planOp = getEndContactPlanOps(paths, nodePath);
            alternateOp.add(planOp);
        }
        combAlternateOp.add(alternateOp);
        return combAlternateOp;
    }

    /**
     * 获取变电站新出现的所有备选集合
     */
    public static CombAlternateOp getBdzNewLineCombAltOp(List<ContactBranch> contactBranches, NodePath nodePath) {

        CombAlternateOp combAlternateOp = new CombAlternateOp();
        AlternateOp alternateOp = new AlternateOp();
        for (ContactBranch contactBranch : contactBranches) {
            List<PlanOperate> planOps = getBdzNewLinePlanOp(contactBranch, nodePath);
            alternateOp.addAll(planOps);
        }
        combAlternateOp.add(alternateOp);
        return combAlternateOp;
    }


    /**
     * 获取末端联络线大分子插入位置
     */
    public static List<PlanOperate> getEndContactPlanOps(ContactBranch contactBranch, NodePath nodePath) {
        return getContactPlanOps(contactBranch, nodePath, PlanOperateFactory::createBranchContact);
    }

    /**
     * 获取端联络线插入位置
     */
    public static PlanOperate getEndContactPlanOps(List<Node> paths, NodePath nodePath) {
        // 当前分支会产生多个点最终确定的联络点的lay
        ProcessContactBo pContactBo = LayPositionUtil.getPContactByPaths(paths, nodePath);
        BaseLay endContact = LayNodeFactory.createEndContact(pContactBo, null, null);

        return PlanOperateFactory.createContact(endContact);
    }


    /**
     * 获取末端联络线大分子插入位置
     */
    public static List<PlanOperate> getBdzNewLinePlanOp(ContactBranch contactBranch, NodePath nodePath) {
        return getContactPlanOps(contactBranch, nodePath, PlanOperateFactory::createBdzNewLine);
    }

    private static List<PlanOperate> getContactPlanOps(ContactBranch contactBranch, NodePath nodePath, BiFunction<BaseLay, BaseLay, PlanOperate> createPlanOp) {
        List<PlanOperate> planOps = new ArrayList<>();

        // 遍历 查找放在首开关的路径
        List<Node> startToFirstBranchPaths = contactBranch.getBranchNode().getStartToFirstBranchPaths(nodePath);

        BaseLay headKgLay = LayNodeFactory.createHeadKg(startToFirstBranchPaths, true);

        // 当前分支会产生多个点最终确定的联络点的lay
        List<ProcessContactBo> pContactByContactLay = LayPositionUtil.getPContactByContactLay(contactBranch, nodePath);
        for (ProcessContactBo processContactBo : pContactByContactLay) {
            BaseLay endContact = LayNodeFactory.createEndContact(processContactBo, headKgLay.getId(), contactBranch);
            PlanOperate branchContact = createPlanOp.apply(headKgLay, endContact);
            planOps.add(branchContact);
        }
        return planOps;
    }

    /**
     * 所有的分支线通过放置的排序
     */
    static void sortBranchPaths(List<List<Node>> branchPaths) {
        branchPaths.sort((paths1, paths2) -> getSortScore(paths2) - getSortScore(paths1));
    }

    /**
     * 各个分支路径排序
     * 优先末端 优先站房并且有剩余的间隔 > 末端是杆塔 > 其它（配变什么的）
     */
    static int getSortScore(List<Node> paths) {
        Node lastNode = paths.get(paths.size() - 1);
        int result = paths.size();
        List<HwgBayBo> hwgStations = NodeUtils.getContactStation(paths);

        // 杆塔
        if (lastNode.isPole()) {
            result = result + 50;
        } else if (!CollectionUtils.isEmpty(hwgStations)) {
            // 表示有环网柜
            Optional<HwgBayBo> optional = hwgStations.stream()
                    .filter(n -> n.getBayNodes() != null && n.getBayNodes().size() >= 2).findFirst();
            HwgBayBo hwg = optional.orElse(null);

            // 环网柜有剩余的间隔可以新增联络
            if (hwg != null) {
                result = result + 40;
            }
        }


        return result;
    }

}
