package com.ruoyi.service.plan.impl;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.util.DoubleFormatter;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederLoadChange;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.mapper.device.PbMapper;
import com.ruoyi.service.calc.measurement.CalcLoadService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.plan.model.contact.RunAdjustHeNodeMap;
import com.ruoyi.service.plan.utils.LayNodeFactory;
import com.ruoyi.service.plan.utils.PlanOperateFactory;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ContactHandleService {

    @Autowired
    IConditionService iConditionService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    PbMapper pbMapper;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IBayQueryService bayQueryService;

    /**
     * 获取线路最大的负载率
     */
    public double getMaxFeederLoad() {
        return (double) iConditionService.lineOverloadNum() / 100;
    }

    /**
     * 获取可以转供联络线开关
     */
    public List<ContactFeederKg> getCanContactFeederKg(String feederId, NodePath nodePath, List<FeederNtVo> contactFeederNts, double maxLoad) {

        // 这里过滤超过最大值的（联络线本身就已经负载不能在转至该线路）
        List<FeederNtVo> meetFeeders = filterLimitFeederNt(contactFeederNts, maxLoad);

        // 过滤掉其它同母的线路
        List<String> busUnderFeederIds = bayQueryService.getBusUnderFeederIds(feederId);

        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();

        return contactFeederKgs.stream().filter(n ->
                // 不存在同母联络线 和 当前联络开关在满足的可以转供的线路
                busUnderFeederIds.stream().noneMatch(fId -> StringUtils.equals(fId, n.getFeederPsrId()))
                        && meetFeeders.stream().anyMatch(f -> StringUtils.equals(f.getPsrId(), n.getFeederPsrId()))
        ).collect(Collectors.toList());
    }

    // =========================== 获取运方调整相关 =======================


    /**
     * 过滤可以调整的联络开关，住上开关和 环网柜或者开闭所内的开关
     */
    List<ContactFeederKg> filterContactKgByType(List<ContactFeederKg> contactFeederKgs, Map<String, Node> nodeMap) {
        // 通过对展示内心集合
        return contactFeederKgs.stream().filter(cFeederKg -> NodeUtils.canContactKgNode(nodeMap.get(cFeederKg.getKgPsrId()))
        ).collect(Collectors.toList());
    }

    /**
     * 获取可以运放调整的所有组合
     */
    public CanRunAdjustFeeder getCanRunAdjust(FeederNtVo mainFeederNt, List<ContactFeederKg> contactFeederKgs, List<FeederNtVo> contactFeederNts, NodePath nodePath, double maxLoad, Map<String, Node> nodeMap) {

        // 1、过滤可以调整的联络开关，住上开关和 环网柜或者开闭所内的开关
        contactFeederKgs = filterContactKgByType(contactFeederKgs, nodeMap);

        // 2、查找负载率相关的
        Map<String, FeederNtVo> contactFeederNtMap = contactFeederNts.stream().collect(Collectors.toMap(FeederNtVo::getPsrId, d -> d));

        // 仅仅减少一部分的
        List<List<FeederTransferCap>> onlySubResult = new ArrayList<>();
        // 能直接满足的 当前线都减少小于最大 并且 联络新增的也小于最大
        List<List<FeederTransferCap>> singleCanResult = new ArrayList<>();

        // 联络联络开关
        for (ContactFeederKg feederKg : contactFeederKgs) {
            FeederNtVo contactFeederNt = contactFeederNtMap.get(feederKg.getFeederPsrId());
            List<FeederTransferCap> onlySubs = new ArrayList<>();
            List<FeederTransferCap> allCans = new ArrayList<>();

            List<FeederTransferCap> runAdjustByContactKg = getRunAdjustByContactKg(feederKg, nodePath, mainFeederNt, contactFeederNt, true);

            if (runAdjustByContactKg == null) {
                continue;
            }

            // 过滤转供配变相同的
            for (FeederTransferCap feederTransferCap : runAdjustByContactKg) {
                // 专供线路负载率小于最大 并且  当前线全转供的也不需要
                if (feederTransferCap.getTfContactChangeLoad() < maxLoad && feederTransferCap.getSourceLoad() > feederTransferCap.getSourceChangeLoad() && feederTransferCap.getSourceChangeLoad() > 0.0) {
                    // 表示当前线路和转供的联络线都满足了最大负载率
                    if (feederTransferCap.getSourceChangeLoad() < maxLoad) {
                        allCans.add(feederTransferCap);
                    } else {
                        // 已经减少
                        onlySubs.add(feederTransferCap);
                    }
                }
            }

            if (!onlySubs.isEmpty()) {
                onlySubResult.add(onlySubs);
            }
            if (!allCans.isEmpty()) {
                singleCanResult.add(allCans);
            }
        }

        // 组合满足的
        List<CombFeederTransfer> combCanFTrs = FindRunAdjustLay.findCombKTrs(onlySubResult, mainFeederNt.getHisMaxLoadRate(), maxLoad);

        return new CanRunAdjustFeeder(onlySubResult, singleCanResult, combCanFTrs);
    }

    /**
     * 根据联络开关  获取所有与他能组合的运放调整列表
     *
     * @param contactFeederKg   联络开个
     * @param nodePath
     * @param mainFeederNt      当前线路线路相关最值值对象
     * @param contactFeederNt   联络开关线路相关最值值对象
     * @param isFilterReplacePb 是否过滤上一个和下一个直接重复的
     */
    List<FeederTransferCap> getRunAdjustByContactKg(ContactFeederKg contactFeederKg, NodePath nodePath, FeederNtVo mainFeederNt, FeederNtVo contactFeederNt, boolean isFilterReplacePb) {

        List<Node> pbList = nodePath.getPbList();
        int contactFeederPbNum = pbMapper.selectFeederPbCount(contactFeederNt.getPsrId());
        List<FeederTransferCap> result = new ArrayList<>();

        // 先查找合闸开关（联络开关） 对应所有的分闸开关集合
        RunAdjustHeNodeMap runAdjustHeNode = getRunAdjustHeNode(contactFeederKg, nodePath);
        if (runAdjustHeNode == null) {
            return null;
        }
        Node heNode = runAdjustHeNode.getHeNode();
        Node nextNode = runAdjustHeNode.getNextNode();

        List<Node> fenNodes = runAdjustHeNode.getFenNodes();
        List<Node> beforePbs = null;
        for (Node fenNode : fenNodes) {
            // 主干路径的开关 需要和联络开关做分段

            List<Node> path = powerSupplyNodes(heNode, nextNode, fenNode);

            // 这些是将要转供走的的配变
            List<Node> pbs = path.stream().filter(Node::isPb).collect(Collectors.toList());

            // 没有配变
            if (CollectionUtils.isEmpty(pbs)) {
                continue;
            }

            // 表示分段和上一个分段的配变一样我们就不需要相同的了
            if (isFilterReplacePb && beforePbs != null && beforePbs.size() == pbs.size()) {
                continue;
            }


            FeederTransferCap feederTransferCap = calcFeederTransferCap(mainFeederNt, contactFeederNt, pbs);

            // 设置分位和合为开关 节点
            feederTransferCap.generateFenHeNode(heNode, fenNode);

            // 转供路径
            feederTransferCap.setPaths(path);
            feederTransferCap.setTfContactPbNum(contactFeederPbNum);
            feederTransferCap.setSourcePbNum(pbList.size());

            result.add(feederTransferCap);
            beforePbs = pbs;
        }
        return result;
    }

    /**
     * 根据联络开关 获取可以组合的远方调整节点映射
     */
    private RunAdjustHeNodeMap getRunAdjustHeNode(ContactFeederKg contactFeederKg, NodePath nodePath) {
        List<ArrayList<Node>> mainNodePaths = nodePath.getMainNodePaths();
        for (List<Node> mainNodePath : mainNodePaths) {
            for (int i = mainNodePath.size() - 1; i >= 0; i--) {
                // 主干路径的开关 需要和联络开关做分段
                if (StringUtils.equals(mainNodePath.get(i).getPsrId(), contactFeederKg.getKgPsrId())) {
                    List<Node> fenNodes = new ArrayList<>();
                    for (int j = i - 1; j >= 0; j--) {
                        Node fenNode = mainNodePath.get(j);
                        // 主干路径的开关 需要和联络开关做分段
                        if (fenNode.isKg("all")) {
                            fenNodes.add(fenNode);
                        }
                    }
                    return new RunAdjustHeNodeMap(mainNodePath.get(i), mainNodePath.get(i - 1), fenNodes, mainNodePath);
                }

            }
        }
        return null;
    }


    // =========================== 计算相关 =======================

    /**
     * 电流流过的供电路径（转供路径）
     *
     * @param heNode  合闸刀联络开关节点
     * @param dirNode 联络开关的方向  主要为了辨别方向
     * @param fenNode 分闸开关节点
     */
    public List<Node> powerSupplyNodes(Node heNode, Node dirNode, Node fenNode) {
        ArrayList<Node> result = new ArrayList<>();
        if (fenNode == null) {
            return result;
        }
        NodeUtils.loopNode(heNode, new HashMap<>(), Arrays.asList(dirNode), node -> {
            result.add(node);
            if (fenNode.equals(node.getId())) {
                return true;
            }
            return false;
        });
        return result;
    }

    /**
     * 电流流过的供电路径（转供路径）
     *
     * @param heNode  合闸刀联络开关节点
     * @param fenNode 分闸开关节点
     */
    public List<Node> powerSupplyNodes(Node heNode, Node fenNode, NodePath nodePath) {
        HashMap<String, Node> contactDirNodeMap = nodePath.getContactDirNodeMap();
        Node dirNode = contactDirNodeMap.get(heNode.getPsrId());
        return powerSupplyNodes(heNode, dirNode, fenNode);
    }

    /**
     * 计算当前线路转供至另一条线路的负载率变化
     *
     * @param mainFeederNt    当前主线路
     * @param contactFeederNt 专供的联络线
     * @param removePbs       减少点陪伴集合
     */
    public FeederTransferCap calcFeederTransferCap(FeederNtVo mainFeederNt, FeederNtVo contactFeederNt, List<Node> removePbs) {
        double totalPbCap = removePbs.stream().mapToDouble(Node::getCap).sum();
        // 当前线路降低之后的负载率
        double subLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

        // 专供的线路上升的负载率
        double addLoad = CalcLoadService.calcFeederLoad(contactFeederNt.getHisMaxLoadRate(), contactFeederNt.getFeederRateCapacity(), totalPbCap);

        FeederTransferCap result = new FeederTransferCap(mainFeederNt.getPsrId(), mainFeederNt.getName(), contactFeederNt.getPsrId(), contactFeederNt.getName());
        // 设置负载率更改
        result.setSourceLoad(mainFeederNt.getHisMaxLoadRate());
        result.setSourceChangeLoad(subLoad);

        result.setTfContactLoad(contactFeederNt.getHisMaxLoadRate());
        result.setTfContactChangeLoad(addLoad);
        return result;
    }

    /**
     * 获取线路转供
     */
    public FeederTransferCap calcFeederTransferCap(String feederId, Node heNode, Node fenNode, NodePath nodePath) {

        // 转供路径
        List<Node> tfrPaths = powerSupplyNodes(heNode, fenNode, nodePath);
        FeederNtVo feederNt = getFeederNt(feederId);

        // 查找合闸开关关联的联络线
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();
        ContactFeederKg contactFeederKg = ListUtils.findFirst(contactFeederKgs, (d) -> StringUtils.equals(d.getKgPsrId(), heNode.getPsrId()));
        FeederNtVo contactFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(contactFeederKg.getFeederPsrId());

        List<Node> pbList = tfrPaths.stream().filter(Node::isPb).collect(Collectors.toList());

        // 容量加装
        processPbNodeCap(pbList);

        FeederTransferCap feederTransferCap = calcFeederTransferCap(feederNt, contactFeederNt, pbList);

        // 设置分位和合为开关 节点
        feederTransferCap.generateFenHeNode(heNode, fenNode);

        // 转供路径
        feederTransferCap.setPaths(tfrPaths);
        feederTransferCap.setSourcePbNum(pbList.size());

        return feederTransferCap;
    }

    /**
     * 获取线路转供
     */
    public FeederTransferCap calcFeederTransferCapByBranchNode(String feederId, String contactFeederId, BranchNode branchNode) {

        // 转供路径
        List<Node> tfrPaths = branchNode.getNodes();
        FeederNtVo feederNt = getFeederNt(feederId);

        FeederNtVo contactFeederNt = feederDeviceMapper.selectFeederNtsByFeederId(contactFeederId);

        List<Node> pbList = tfrPaths.stream().filter(Node::isPb).collect(Collectors.toList());

        // 容量加装
        processPbNodeCap(pbList);

        FeederTransferCap feederTransferCap = calcFeederTransferCap(feederNt, contactFeederNt, pbList);

        // 转供路径
        feederTransferCap.setPaths(tfrPaths);
        feederTransferCap.setSourcePbNum(pbList.size());

        return feederTransferCap;
    }


    // =========================== 小方法 =======================

    /**
     * 将所有的配变加工容量
     *
     * @param nodes
     */
    public void processPbNodeCap(List<Node> nodes) {
        List<Node> pbNodes = nodes.stream().filter(Node::isPb).collect(Collectors.toList());

        //计算每个分支的容量
        List<String> idList = pbNodes.stream().map(Node::getPsrId).collect(Collectors.toList());
        List<Double> caps = queryDeviceInfo.selectDeviceRatedCapacity(idList);

        // 加工容量 按照顺序
        for (int i = 0; i < caps.size(); i++) {
            pbNodes.get(i).setCap(caps.get(i));
        }
    }

    /**
     * 过滤掉重过载线路
     */
    public List<DeviceFeeder> filterOverLoadFeeder(List<DeviceFeeder> feederList) {
        double maxLoad = (double) iConditionService.lineOverloadNum() / 100;   // 当前配置最大负载率

        List<FeederNtVo> feederNtVos = feederDeviceMapper.selectFeederNtsByFeederIds(feederList.stream().map(DeviceFeeder::getPsrId).collect(Collectors.toList()));

        // 过滤掉重过载
        List<FeederNtVo> meetFeeders = filterLimitFeederNt(feederNtVos, maxLoad);

        return feederList.stream().filter(n ->
                // 当前线路在满足的线路里
                meetFeeders.stream().anyMatch(f -> StringUtils.equals(f.getPsrId(), n.getPsrId()))
        ).collect(Collectors.toList());
    }

    /**
     * 过滤掉重过载线路
     */
    public List<String> getOverLoadFeeder(List<String> feederIds) {
        double maxLoad = (double) iConditionService.lineOverloadNum() / 100;   // 当前配置最大负载率

        List<FeederNtVo> feederNtVos = feederDeviceMapper.selectFeederNtsByFeederIds(feederIds);

        // 过滤掉重过载
        List<FeederNtVo> meetFeeders = filterLimitFeederNt(feederNtVos, maxLoad);

        return feederIds.stream().filter(feederId ->
                // 当前线路在满足的线路里
                meetFeeders.stream().noneMatch(f -> StringUtils.equals(f.getPsrId(), feederId))
        ).collect(Collectors.toList());
    }

    /**
     * 过滤不重过载的线路集合
     *
     * @param feederNtVos 线路最大量测对象
     * @param maxLoad     最大重过载
     */
    public List<FeederNtVo> filterLimitFeederNt(List<FeederNtVo> feederNtVos, double maxLoad) {
        return feederNtVos.stream().filter(n ->
                // 线路负载率和装机容量 不为空 并且负载率小于最大值
                n.getHisMaxLoadRate() != null && n.getFeederRateCapacity() != null && n.getHisMaxLoadRate() < maxLoad
        ).collect(Collectors.toList());
    }

    /**
     * 获取线路当前负载率
     *
     * @return
     */
    public double getFeederLoad(String feederId) {
        FeederNtVo feederNt = getFeederNt(feederId);
        return feederNt.getHisMaxLoadRate();
    }

    /**
     * 获取线路
     *
     * @return
     */
    public FeederNtVo getFeederNt(String feederId) {
        return feederDeviceMapper.selectFeederNtsByFeederId(feederId);
    }

}
