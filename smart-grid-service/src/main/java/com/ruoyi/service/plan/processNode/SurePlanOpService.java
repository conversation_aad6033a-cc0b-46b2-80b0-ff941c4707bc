package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.TopologyChangeService;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.lay.KgLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.ContactLayHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理加工节点放置位置实体类
 */
@Slf4j
@Component
public class SurePlanOpService {

    @Autowired
    ContactLayHandle contactLayHandle;

    @Autowired
    TopologyChangeService topologyChangeService;

    @Autowired
    ContactHandleService contactHandleService;

    public SurePlanOp surePlanOpHandle(String feederId, CombAlternateOp combAlternate, NodePath nodePath) {
        double feederLoad = contactHandleService.getFeederLoad(feederId);  // 线路负载率
        double maxFeederLoad = contactHandleService.getMaxFeederLoad(); // 线路最大负载率

        SurePlanOp surePlanOp = toSurePlanOp(combAlternate);

        // 满足的方案操作
        List<PlanOperate> meetPlanOps = new ArrayList<>();
        // List<PlanOperate> notMeetPlanOps = new ArrayList<>();

        // 处理所有的组合方案
        for (PlanOperate planOp : surePlanOp.getPlanOps()) {
            try {
                // 加装负载率
                handlePlanOpLoad(planOp, feederId, nodePath, feederLoad, maxFeederLoad);
            } catch (Exception e) {
                log.error("加装负载率异常!", e);
               // continue;
            }

            // 1、判断是否满足负载率 在加工联络点的时候 其实已经也判断力 这里我们就别在判断了
            //   if (planOp.canMeetMaxLoad()) {
            meetPlanOps.add(planOp);
            //    } else {
            //  notMeetPlanOps.add(planOp);
            //   }
        }

        List<PlanOperate> resultPlanOps = new ArrayList<>(meetPlanOps);
        // 满足的很少 我们也不把一些不满足的放进来
//        if (resultPlanOps.size() < 3) {
//            resultPlanOps.addAll(notMeetPlanOps);
//        }
        // TODO 后续后期 按照排序：联络线路长度、负载率
        // TODO 不同的相关操作 也可以 都展示 比如每种联络线只能一个、打开的开关和联络开个组合等等

        return SurePlanOp.createSurePlanOp(resultPlanOps);
    }

    public SurePlanOp toSurePlanOp(CombAlternateOp combAlternate) {
        // 去空
        combAlternate.filterEmpty();
        // 去重
        //  distinctLay(layPositionList);

        SurePlanOp surePlanOp = combAlternate.toSurePlanOp();
        // TODO 应该也要使用造价进行排序
        surePlanOp.sort(); // 排序

        return surePlanOp;
    }


    /**
     * 处理单个planOperate负载率变化
     */
    private void handlePlanOpLoad(PlanOperate planOperate, String feederId, NodePath nodePath, double currentLoad, double maxLoad) {
        // 如果没有联络类型 不需要设置
        if (!planOperate.getLayNodeList().stream().anyMatch(d -> d.isContactType() || d.isRunAdjust())) {
            return;
        }

        List<FeederTransferCap> fTrs = new ArrayList<>();
        // lay映射
        Map<String, BaseLay> layMap = planOperate.getLayNodeList().stream().collect(Collectors.toMap(BaseLay::getId, d -> d));

        // 获取当前planOp更改导致的拓扑更改nodePath对象
        List<Node> operateData = planOperate.toOperateData();
        List<Node> nodes = NodeUtils.copyNodes(operateData);
        nodePath = topologyChangeService.changeNodePath(nodePath, nodes);

        // 处理负载率
        for (BaseLay layNode : planOperate.getLayNodeList()) {
            // 通过联络开关的并且和关联的lay 会导致负载率变化
            if (layNode.isContactType()) {
                ContactLay contactLay = (ContactLay) layNode;
                if (StringUtils.isBlank(contactLay.getLinkLayId())) {
                    continue;
                }
                KgLay linkLay = (KgLay) layMap.get(contactLay.getLinkLayId());
                if (linkLay.isKgType()) {
                    Node fenNode = null;
                    // 当前线路
                    Node heNode = contactLay.getContactKg();
                    for (Node node : linkLay.getLayNodes()) {
                        if (node.getSwitchOpen() != null && node.getSwitchOpen()) {
                            fenNode = node;
                            break;
                        }
                    }

                    if (fenNode != null && heNode != null) {
                        // 由于上面我们是复制出来的 这里和上面的不是同一个对象  我们这里需要注意一下
                        heNode = nodePath.getNodeByPsrId(StringUtils.isNotBlank(heNode.getPsrId()) ? heNode.getPsrId() : heNode.getId());
                        fenNode = nodePath.getNodeByPsrId(StringUtils.isNotBlank(fenNode.getPsrId()) ? fenNode.getPsrId() : fenNode.getId());

                        try {
                            nodePath.getContactDirNodeMap().get(heNode.getPsrId());
                        } catch (Exception e) {
                            System.out.println("aaa");
                        }

                        Node dirNode = nodePath.getContactDirNodeMap().get(heNode.getPsrId());
                        // TODO 自定义节点 不应该是联络开关  应该是环网柜的进线开关 这里我们默认使用当前环网柜下的其他联络开关
                        if (dirNode == null && heNode.isContactStationInNode()) {
                            for (Node child : heNode.getParent().getChildren()) {
                                if (!child.equals(heNode.getId()) && nodePath.getContactKgNodes().stream().anyMatch(d -> d.equals(child.getId()))) {
                                    heNode = child;
                                    break;
                                }
                            }
                        }

                        FeederTransferCap feederTransferCap = contactHandleService.calcFeederTransferCap(feederId, heNode, fenNode, nodePath);
                        fTrs.add(feederTransferCap);

                    }
                }
            }
        }

        if (CollectionUtils.isNotEmpty(fTrs)) {
            // planOperate.setFeederTransferCaps(fTrs);
            planOperate.setCombFeederTransfer(FindRunAdjustLay.getCombFTr(fTrs, currentLoad, maxLoad));
        }

    }

}
