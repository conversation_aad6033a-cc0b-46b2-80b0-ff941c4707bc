package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.BdzNewLineInfo;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.TopologyChangeService;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.service.plan.model.lay.KgLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.ContactLayHandle;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 处理加工节点放置位置实体类
 */
@Slf4j
@Component
public class SurePlanOpService {

    @Autowired
    ContactLayHandle contactLayHandle;

    @Autowired
    TopologyChangeService topologyChangeService;

    @Autowired
    ContactHandleService contactHandleService;

    public SurePlanOp surePlanOpHandle(String feederId, SurePlanOp surePlanOp, NodePath nodePath) {
        double feederLoad = contactHandleService.getFeederLoad(feederId);  // 线路负载率
        double maxFeederLoad = contactHandleService.getMaxFeederLoad(); // 线路最大负载率

        // 去空
        surePlanOp.filterEmpty();

        // 满足的方案操作
        List<PlanOperate> meetPlanOps = new ArrayList<>();
        // List<PlanOperate> notMeetPlanOps = new ArrayList<>();

        // 处理所有的组合方案
        for (PlanOperate planOp : surePlanOp.getPlanOps()) {
            try {
                // 加装负载率
                handlePlanOpLoad(planOp, feederId, nodePath, feederLoad, maxFeederLoad);
            } catch (Exception e) {
                log.error("加装负载率异常!", e);
                // continue;
            }

            // 1、判断是否满足负载率 在加工联络点的时候 其实已经也判断力 这里我们就别在判断了
            //   if (planOp.canMeetMaxLoad()) {
            meetPlanOps.add(planOp);
            //    } else {
            //  notMeetPlanOps.add(planOp);
            //   }
        }

        List<PlanOperate> resultPlanOps = new ArrayList<>(meetPlanOps);
        // 满足的很少 我们也不把一些不满足的放进来
//        if (resultPlanOps.size() < 3) {
//            resultPlanOps.addAll(notMeetPlanOps);
//        }
        // TODO 后续后期 按照排序：联络线路长度、负载率
        // TODO 不同的相关操作 也可以 都展示 比如每种联络线只能一个、打开的开关和联络开个组合等等
        // TODO 应该也要使用造价进行排序

        SurePlanOp sureResult = SurePlanOp.createSurePlanOp(resultPlanOps);
        // 排序
        sureResult.sort();

        return sureResult;
    }

    /**
     * 处理单个planOperate负载率变化
     */
    private void handlePlanOpLoad(PlanOperate planOperate, String feederId, NodePath nodePath, double currentLoad, double maxLoad) {
        // 如果没有联络类型 不需要设置
        if (!planOperate.getLayNodeList().stream().anyMatch(d -> d.isContactType() || d.isRunAdjust())) {
            return;
        }

        // lay映射
        Map<String, BaseLay> layMap = planOperate.getLayNodeList().stream().collect(Collectors.toMap(BaseLay::getId, d -> d));

        // 获取当前planOp更改导致的拓扑更改nodePath对象
        List<Node> operateData = planOperate.toOperateData();
        List<Node> nodes = NodeUtils.copyNodes(operateData);
        nodePath = topologyChangeService.changeNodePath(nodePath, nodes);

        // 处理负载率
        List<FeederTransferCap> fTrs;
        if (StringUtils.equals(planOperate.getType(), PlanOperate.BDZ_NEW_LINE_TYPE)) {
            fTrs = getBdzNewLineFTrs(planOperate, feederId, layMap, nodePath);
        } else {
            fTrs = getContactFTrs(planOperate, feederId, layMap, nodePath);
        }

        if (CollectionUtils.isNotEmpty(fTrs)) {
            // planOperate.setFeederTransferCaps(fTrs);
            planOperate.setCombFeederTransfer(FindRunAdjustLay.getCombFTr(fTrs, currentLoad, maxLoad));
        }

    }

    // =========================== 获取当前确定方案的转供负载量 =======================

    /**
     * 临近新路转供
     */
    List<FeederTransferCap> getContactFTrs(PlanOperate planOperate, String feederId, Map<String, BaseLay> layMap, NodePath nodePath) {
        List<FeederTransferCap> fTrs = new ArrayList<>();
        // 处理负载率
        for (BaseLay layNode : planOperate.getContactLays()) {
            // 通过联络开关的并且和关联的lay 会导致负载率变化
            ContactLay contactLay = (ContactLay) layNode;
            if (StringUtils.isBlank(contactLay.getLinkLayId())) {
                continue;
            }
            KgLay linkLay = (KgLay) layMap.get(contactLay.getLinkLayId());
            if (linkLay.isKgType()) {
                Node fenNode = linkLay.getFenNode();
                // 当前线路
                Node heNode = contactLay.getContactKg();

                if (fenNode != null && heNode != null) {
                    List<Node> fenHeNode = getChangeFenHeNode(fenNode, heNode, nodePath);
                    fenNode = fenHeNode.get(0);
                    heNode = fenHeNode.get(1);

                    FeederTransferCap feederTransferCap = contactHandleService.calcFeederTransferCap(feederId, heNode, fenNode, nodePath);
                    fTrs.add(feederTransferCap);

                }
            }
        }
        return fTrs;
    }

    /**
     * 变电站转供
     */
    List<FeederTransferCap> getBdzNewLineFTrs(PlanOperate planOperate, String feederId, Map<String, BaseLay> layMap, NodePath nodePath) {
        List<FeederTransferCap> fTrs = new ArrayList<>();
        // 处理负载率
        for (BaseLay layNode : planOperate.getContactLays()) {
            // 通过联络开关的并且和关联的lay 会导致负载率变化
            ContactLay contactLay = (ContactLay) layNode;
            if (StringUtils.isBlank(contactLay.getLinkLayId())) {
                continue;
            }
            KgLay linkLay = (KgLay) layMap.get(contactLay.getLinkLayId());
            if (linkLay.isKgType()) {
                Node fenNode = linkLay.getFenNode();
                // 当前线路
                Node heNode = contactLay.getBdzNewLineContactKg();
                BdzNewLineInfo bdzNewLine = heNode.getBdzNewLine();

                if (fenNode != null && heNode != null && bdzNewLine != null) {
                    List<Node> fenHeNode = getChangeFenHeNode(fenNode, heNode, nodePath);
                    fenNode = fenHeNode.get(0);
                    heNode = fenHeNode.get(1);

                    FeederTransferCap feederTransferCap = contactHandleService.calcBdzTransferCap(feederId, heNode, fenNode, nodePath, bdzNewLine);
                    fTrs.add(feederTransferCap);

                }
            }
        }
        return fTrs;
    }

    // 由于上面我们是复制出来的 这里和上面的不是同一个对象  我们这里需要注意一下
    // 重新获取设置 保持同一个
    List<Node> getChangeFenHeNode(Node fenNode, Node heNode, NodePath nodePath) {
        // 由于上面我们是复制出来的 这里和上面的不是同一个对象  我们这里需要注意一下
        heNode = nodePath.getNodeByPsrId(StringUtils.isNotBlank(heNode.getPsrId()) ? heNode.getPsrId() : heNode.getId());
        fenNode = nodePath.getNodeByPsrId(StringUtils.isNotBlank(fenNode.getPsrId()) ? fenNode.getPsrId() : fenNode.getId());

        return Arrays.asList(fenNode, heNode);
    }
}
