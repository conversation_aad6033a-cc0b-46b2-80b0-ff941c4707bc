package com.ruoyi.service.plan.model.lay;

import cn.hutool.core.collection.CollectionUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.PlanProcessUtils;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 新上联络线
 */
@Data
public class ContactLay extends BaseLay {

    public ContactLay(String type, ProcessContactBo processContactBo) {
        this.type = type;
        this.processContactBo = processContactBo;
    }

    // 当前已经生成好的加工联络Vo
    private ProcessContactVo processContactVo;

    // 准备生成的加工联络Bo
    private ProcessContactBo processContactBo;

    // private NearNode nearNode;

    ContactBranch contactBranch;

    /**
     * 关联的lay集合
     */
    private String linkLayId;

    public String getContactFeederId() {
        if (processContactVo == null) {
            return null;
        }
        return processContactVo.getContactFeederId();
    }

    public String getContactFeederName() {
        if (processContactVo == null) {
            return null;
        }
        return processContactVo.getContactFeederName();
    }

    @Override
    public List<Node> getLayNodes() {
        if (processContactVo == null) {
            return new ArrayList<>();
        }
        return processContactVo.getContactNodeList();
    }

    @Override
    public int getScope() {
        List<Node> contactNodes = getLayNodes();
        if (CollectionUtil.isNotEmpty(contactNodes)) {
            if (processContactBo != null) {
                String pType = processContactBo.getType();
                if (StringUtils.equals(pType, ProcessContactBo.POLE_RANGE)) {
                    return 70;
                } else if (StringUtils.equals(pType, ProcessContactBo.STATION_BAY)) {
                    return 80;
                } else if (StringUtils.equals(pType, ProcessContactBo.SEG_ADD_HWG)) {
                    return 50;
                } else if (StringUtils.equals(pType, ProcessContactBo.HWG_REPLACE)) {
                    return 50;
                }
            } else {
                return 70;
            }
        }
        return 100;
    }

    @Override
    public List<Object> toStuStr() {
        if (CollectionUtils.isEmpty(getLayNodes())) {
            return new ArrayList<>();
        }
        Node firstNode = getLayNodes().get(0);
        ArrayList<Object> strList = new ArrayList<>();

        // 设备上新增联络线
        if (firstNode != null) {
            String[] parentWhg = firstNode.getParentWhg();
            strList.add("在");
            if (parentWhg != null) {
                strList.add(PlanProcessUtils.toTextBtnStu(parentWhg[0], parentWhg[1], parentWhg[2], null, null));
                strList.add("剩余间隔的");
            }

            strList.add(PlanProcessUtils.toTextBtnStu(firstNode.getPsrId(), firstNode.getPsrType(), firstNode.getPsrName(), null, null));
            strList.add("设备上新增联络线，");
        }

        // 联络线
        if (getContactFeederId() != null) {
            strList.add("连接至");
            strList.add(PlanProcessUtils.toTextBtnStu(getContactFeederId(), "dkx", getContactFeederName(), null, null));
            strList.add("线路");
        }
        return strList;
    }

    /**
     * 获取联络开关
     */
    public Node getContactKg() {
        List<Node> contactNodes = getLayNodes();
        if (CollectionUtils.isEmpty(contactNodes)) {
            return null;
        }

        return ListUtils.findFirst(contactNodes, (node) -> node.getContactFeeder() != null);
    }

    public double getContactLength() {
        if (processContactVo == null) {
            return 0.0;
        }
        return processContactVo.getTotalLength();
    }
}
