package com.ruoyi.service.plan.processNode.layHandle.contactHandle;

import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.entity.device.DeviceHWG;
import com.ruoyi.entity.device.StationKg;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.entity.map.vo.ProcessContactVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.plan.model.lay.ContactLay;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.mapper.device.DeviceHwgMapper;
import com.ruoyi.mapper.device.KgMapper;
import com.ruoyi.service.plan.IContactHandle;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.*;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.plan.processNode.ProcessNearNode;
import com.ruoyi.util.coordinates.CoordinateConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 间隔出线的处理
 */
@Component
public class BayContactHandle extends ContactProcess implements IContactHandle {

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    KgMapper kgMapper;

    @Autowired
    DeviceHwgMapper deviceHwgMapper;

    @Autowired
    ProcessNearNode processNearNode;

    @Override
    public void handleContact(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbyDeviceInfoVo> nearDevs, String token) {

        // 附近节点
        NearNode nearNode = toNearNode(lay);

        // 加工
        processNearNode.processDevRoute(nearNode, nearDevs, token);

        ProcessContactVo processContactVo = processContactHandle(nearNode, ProcessContactBo.STATION_BAY);

        lay.setProcessContactVo(processContactVo);
    }

    @Override
    public void handleBdzNewLine(Long problemId, ContactLay lay, PlanOperate planOperate, List<NearbySubstationInfoVo> bdzBays, String token) {

        // 附近节点
        NearNode nearNode = toNearNode(lay);

        // 加工最近的变电站
        processNearNode.processBdzRoute(nearNode, bdzBays, token);

        // 每个设备都对应相关联的联络对象集合
        ProcessContactVo processContactVo = processBdzNewLineHandle(nearNode, ProcessContactBo.STATION_BAY);

        // 加装
        lay.setProcessContactVo(processContactVo);
    }

    private NearNode toNearNode(ContactLay lay) {
        HwgBayBo hwgBay = lay.getProcessContactBo().getHwgBay();
        Node node = processBayContact(hwgBay);
        if (node == null) {
            return null;
        }
        return toNearNode(node);
    }

    /**
     * 加装设置具体要设置的间隔节点 主要设置需要联络的开关node和开关的坐标
     */
    Node processBayContact(HwgBayBo hwgBay) {
        List<Node> bayNodes = hwgBay.getBayNodes();
        Node hwg = hwgBay.getHwg();
        if (CollectionUtils.isEmpty(bayNodes)) {
            return null;
        }

        Node result = null;

        // 加装站内开关（间隔设备）
        List<StationKg> stationKgs = kgMapper.selectStationKgByPsrIds(bayNodes.stream().map(Node::getPsrId).collect(Collectors.toList()));

        // 过滤空坐标
        stationKgs = stationKgs.stream().filter(StationKg::isNotEmptyLngLat).collect(Collectors.toList());

        // 空的坐标
        if (CollectionUtils.isEmpty(stationKgs)) {
            // 使用环网柜
            DeviceHWG deviceHwg = deviceHwgMapper.selectByPsrId(hwg.getPsrId());
            if (deviceHwg != null && StringUtils.isNotBlank(deviceHwg.getGeoPositon())) {
                List<Double> doubles = CoordinateConverter.parseCommaCoordinates(deviceHwg.getGeoPositon(), ",");
                result = bayNodes.get(0);
                result.setGeometry(CoordinateConverter.toPoint(doubles));
            }
        } else {
            // 取第0个即可
            StationKg stationKg = stationKgs.get(0);

            StationKg tmp = stationKg;
            Node node = NodeUtils.findNode(bayNodes, n -> StringUtils.equals(tmp.getPsrId(), n.getPsrId()));
            Point point = CoordinateConverter.toPoint(stationKg.getLongitude(), stationKg.getLatitude());

            if (node != null) {
                // 重新复制一个新的节点 以防当前节点关联之前很多的节点
                result = node.clone();
                result.setGeometry(point);

            }
        }

        //  设置父级节点 环网柜
        if (result != null) {
            result.setParentWhg(hwg.getPsrId(), hwg.getPsrType(), hwg.getPsrName());
        }
        return result;
    }
}
