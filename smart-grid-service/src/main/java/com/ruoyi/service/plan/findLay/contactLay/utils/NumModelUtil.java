package com.ruoyi.service.plan.findLay.contactLay.utils;

import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.service.calc.measurement.CalcLoadService;
import com.ruoyi.service.plan.model.findLay.NodeNumModel;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;


public class NumModelUtil {

    // 容量
    public static List<NodeNumModel> toCapNum(List<Node> edges, Node node, HashMap<String, BranchNode> branchNodeMap) {
        List<NodeNumModel> nodeNums = edges.stream().map(edge -> {
            String branchId = BranchNode.processId(node, edge);
            BranchNode branchNode = branchNodeMap.get(branchId);
            if (branchNode == null) {
                return null;
            }
            NodeNumModel result = new NodeNumModel(branchNode.getPbNodeNum(), node, branchNode);
            result.setNextNode(edge);
            List<Node> pbNodes = branchNode.getPbNodes();
            // 容量值
            double sum = pbNodes.stream().mapToDouble(Node::getCap).sum();
            result.setTotalCap(sum);
            result.setBranchNode(branchNode);
            return result;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 从大到小排序
        nodeNums.sort((n1, n2) -> n2.getNum() - n1.getNum());
        return nodeNums;
    }

    // 配变数量
    public static List<NodeNumModel> toPbNums(List<Node> edges, Node node, HashMap<String, BranchNode> branchNodeMap) {
        List<NodeNumModel> nodeNums = edges.stream().map(edge -> {
            String branchId = BranchNode.processId(node, edge);
            BranchNode branchNode = branchNodeMap.get(branchId);
            NodeNumModel nodeNumModel = new NodeNumModel(branchNode.getPbNodeNum(), node, edge);
            nodeNumModel.setBranchNode(branchNode);
            return nodeNumModel;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 从大到小排序
        nodeNums.sort((n1, n2) -> n2.getNum() - n1.getNum());
        return nodeNums;
    }

    /**
     * 判断大分子
     * @param nodeNum
     * @param minPbNum 最小配变数量
     * @param minCap 最小容量
     * @return
     */
    public static boolean judgeBigBranch(NodeNumModel nodeNum, Integer minPbNum, Integer minCap) {
        int pbNodeNum = nodeNum.getNum();
        double pbNodeCap = nodeNum.getTotalCap();
        return pbNodeNum >= minPbNum || pbNodeCap >= minCap;
    }

    /**
     * 判断配变数量
     * @param nodeNum
     * @param maxNum
     * @return
     */
    public static boolean judgePbNumModel(NodeNumModel nodeNum, int maxNum) {
        int pbNodeNum = nodeNum.getNum();
        return pbNodeNum >= maxNum;
    }

    /**
     * 负载率变化比较
     *
     * @param nodeNum
     * @param mainFeederNt 主问题线路Nt
     * @param maxLoad      最大负载率
     * @param judgeFunc
     */
    public static boolean judgeFeederLoad(NodeNumModel nodeNum, FeederNtVo mainFeederNt, double maxLoad, BiFunction<List<Node>, Double, Boolean> judgeFunc) {
        // int pbNodeNum = nodeNum.getNum();
        BranchNode branchNode = nodeNum.getBranchNode();
        // 判断和满足的
        List<Node> pbs = branchNode.getPbNodes();

        // 没有配变或者
        if (CollectionUtils.isEmpty(pbs)) {
            return false;
        }

        // Double currentLoad = mainFeederNt.getHisMaxLoadRate();
        double totalPbCap = pbs.stream().mapToDouble(Node::getCap).sum();

        // 当前线路降低之后的负载率
        double changeLoad = CalcLoadService.calcFeederLoad(mainFeederNt.getHisMaxLoadRate(), mainFeederNt.getFeederRateCapacity(), -totalPbCap);

        // 负载率不满足
        if (changeLoad >= maxLoad || changeLoad <= 0.0) {
            return false;
        }

        // 如果有自定义判断方法并且不满足的话，直接跳过
        if (judgeFunc != null && !judgeFunc.apply(pbs, changeLoad)) {
            return false;
        }
        return true;
    }
}
