package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.map.IMapService;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.model.lay.BaseLay;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.graph.utils.NodeFactory;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.processNode.layHandle.KgLayHandle;
import com.ruoyi.service.plan.processNode.layHandle.contactHandle.ContactLayHandle;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.ListUtils;
import com.ruoyi.util.NearDeviceInfoUtil;
import com.ruoyi.util.coordinates.CoordinateConverter;
import com.ruoyi.vo.BusbarSwitchVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class ProcessNodeService implements IProcessNodeService {

    @Autowired
    NodeFactory nodeFactory;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ContactLayHandle contactLayHandle;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    SurePlanOpService surePlanOpService;

    @Autowired
    private KgLayHandle kgLayHandle;

    @Autowired
    IMapService mapService;

    @Autowired
    IBayQueryService bayQueryService;

    /**
     * 加工处理 并且返回确定的方案操作
     *
     * @param problemId       问题ID
     * @param combAlternateOp 备选方案操作
     * @param token           调用思极地图路径的需要的思极登录token（前端可以直接获取）
     * @param feederId        当前问题线路ID
     * @param nodePath        拓扑
     * @return 返回确定方案操作
     */
    @Override
    public SurePlanOp handleLayOperateNode(Long problemId, CombAlternateOp combAlternateOp, String token, String feederId, NodePath nodePath) {

        // =========================== 获取本线路附近的杆塔、环网柜、开关站、变电站 =======================

        // 附近杆塔、环网柜、开关站
        List<NearbyDeviceInfoVo> nearDevs = getNearDev(feederId);

        // 附近变电站
        List<NearbySubstationInfoVo> nearBdzs = getNearBdz(feederId);

        // =========================== 加工处理所有备选的lay（提前设置好每个lay需要设置的节点） =======================
        // 运放调整和替换间隔（目前不需要前面最开始已经处理好了，后续需要在加）

        // 提前处理附近联络对象
        contactLayHandle.handleNearLayContact(problemId, feederId, token, combAlternateOp, nearDevs, nearBdzs);

        // 处理处理开关
        kgLayHandle.handleKgLay(combAlternateOp);

        // =========================== 确定的组合，约束限制 =======================
        // 处理加工节点放置位置列表
        SurePlanOp surePlanOp = surePlanOpService.surePlanOpHandle(feederId, combAlternateOp, nodePath);

        return surePlanOp;
    }

    /**
     * 附近杆塔、环网柜、开关站
     * （需过滤重过载，同母联络线的相关线路的，以及没有有剩余间隔的环网柜和开关站）
     */
    List<NearbyDeviceInfoVo> getNearDev(String feederId) {
        // 附近联络点
        List<NearbyDeviceInfoVo> devices = mapService.queryNearbyDevices(feederId, 1500);

        // 当前设备的所有线路ID
        List<String> feederIds = NearDeviceInfoUtil.getAllFeederIds(devices);

        // =========================== 过滤：重过载，同母联络线 =======================
        List<String> overLoadFeeders = contactHandleService.getOverLoadFeeder(feederIds); // 重过载线路
        List<String> busUnderFeederIds = bayQueryService.getBusUnderFeederIds(feederId);   // 同母线路
        List<String> filterFeederIds = Stream.concat(overLoadFeeders.stream(), busUnderFeederIds.stream())
                .collect(Collectors.toList());
        if (!filterFeederIds.contains(feederId)) {
            filterFeederIds.add(feederId);
        }

        devices = NearDeviceInfoUtil.filterByFeederIds(devices, filterFeederIds);

        // =========================== 过滤：没有有剩余间隔的环网柜和开关站，可以的联络点设备，和空坐标设备 =======================
        devices = devices.stream().filter(dev -> {
            if (!dev.isContact() || dev.getLngLat() == null) {
                return false;
            }
            // 站房类型的 必须有剩余间隔
            if (dev.isContactStation()) {
                return dev.getRemainingBayCount() != null && dev.getRemainingBayCount() >= 1;
            }
            return true;
        }).collect(Collectors.toList());

        return devices;
    }

    /**
     * 获取附近变电站
     * 需要过滤有剩余间隔的（剩余主变）
     */
    List<NearbySubstationInfoVo> getNearBdz(String feederId) {
        // 附近变电站
        List<NearbySubstationInfoVo> nearBdzList = mapService.queryNearbySubstationsByNet(feederId, 3000);

        // TODO 需要过滤同母线的间隔

        // 过滤没有坐标和没有剩余间隔的变电站
        nearBdzList = nearBdzList.stream().filter(bdz -> bdz.getLngLat() != null && CollectionUtils.isNotEmpty(bdz.getBusbarSwitchVoList())).collect(Collectors.toList());

        // 处理备用开关坐标（没有就需要处理）
        for (NearbySubstationInfoVo bdz : nearBdzList) {
            String geoPositon = bdz.getGeoPositon();
            double[] lngLat = CoordinateConverter.parseLngLat(geoPositon);
            List<BusbarSwitchVo> busbarSwitchVoList = bdz.getBusbarSwitchVoList();
            for (BusbarSwitchVo busbarSwitchVo : busbarSwitchVoList) {
                if (busbarSwitchVo.getLngLat() != null) {
                    busbarSwitchVo.setLngLat(lngLat);
                }
            }
        }

        return nearBdzList;
    }

    /**
     * 去重 TODO 判断equals有点问题 不同类型比较应该不一样
     */
    private void distinctLay(List<List<ArrayList<BaseLay>>> layNodes) {
        for (List<ArrayList<BaseLay>> oneLays : layNodes) {
            for (int i = 0; i < oneLays.size(); i++) {
                // 最终去重后的结果
                ArrayList<BaseLay> distinctLays = new ArrayList<>();
                // 去重
                for (BaseLay ontLay : oneLays.get(i)) {
                    if (!distinctLays.stream().anyMatch(d -> d.equals(ontLay))) {
                        distinctLays.add(ontLay);
                    }
                }
                oneLays.set(i, distinctLays);
            }
        }
    }
}
