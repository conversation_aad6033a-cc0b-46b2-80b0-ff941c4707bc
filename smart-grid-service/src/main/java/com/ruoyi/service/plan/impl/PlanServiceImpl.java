package com.ruoyi.service.plan.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.ruoyi.common.core.domain.PageQuery;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.device.*;
import com.ruoyi.entity.exception.PlanGenerationException;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.plan.PlanAnalysisState;
import com.ruoyi.entity.plan.bo.PlanBo;
import com.ruoyi.entity.plan.vo.PlanCost;
import com.ruoyi.entity.plan.vo.PlanGroupResultVo;
import com.ruoyi.entity.plan.vo.PlanGroupVo;
import com.ruoyi.entity.plan.vo.PlanVo;
import com.ruoyi.entity.problem.Problem;
import com.ruoyi.entity.problem.ProblemSchemeAnalysis;
import com.ruoyi.entity.problem.TaskInfo;
import com.ruoyi.entity.problem.vo.ProblemVo;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.graph.vo.BranchNodeVo;
import com.ruoyi.mapper.device.DeviceAccessPointMapper;
import com.ruoyi.mapper.device.DevicePoleTransformerMapper;
import com.ruoyi.mapper.device.DeviceStationTransformerMapper;
import com.ruoyi.mapper.device.StationServiceTransformerMapper;
import com.ruoyi.mapper.plan.PlanAnalysisStateMapper;
import com.ruoyi.mapper.plan.PlanMapper;
import com.ruoyi.mapper.problem.ProblemMapper;
import com.ruoyi.service.cost.ICostService;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.IPlanService;
import com.ruoyi.service.plan.generatePlan.*;
import com.ruoyi.service.plan.model.GeneratePlanBo;
import com.ruoyi.service.plan.model.plan.PlanOperate;
import com.ruoyi.service.znap.IBayQueryService;
import com.ruoyi.util.ListUtils;
import com.ruoyi.vo.BusbarSwitchVo;
import com.ruoyi.vo.ContactSwitchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.ruoyi.entity.plan.PlanAnalysisState.*;

/**
 * 故障解决方案Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-26
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class PlanServiceImpl implements IPlanService {

    @Autowired
    ICostService costService;
    // 分段配变不合理生产方案
    @Autowired
    SegPbMuchPlan segPgMuchPlan;

    @Autowired
    private ProblemMapper problemMapper;

    @Autowired
    PlanProcessServiceImpl planProcessService;

    private final PlanMapper planMapper;

    @Autowired
    PlanAnalysisStateMapper planAnalysisStateMapper;

    @Autowired
    SingleRadiationPlan singleRadiationPlan;

    @Autowired
    FeederPbMuchPlan feederPbMuchPlan;

    @Autowired
    BigBranchPlan bigBranchPlan;

    @Autowired
    FeederOverloadPlan feederOverloadPlan;

    @Autowired
    SameBusContactPlan sameBusContactPlan;

    @Resource
    ISingMapService singMapService;

    @Resource
    private IBayQueryService bayQueryService;

    @Resource //0303
    private StationServiceTransformerMapper stationServiceTransformerMapper;
    @Resource //0302
    private DeviceStationTransformerMapper deviceStationTransformerMapper;

    @Resource
    private DevicePoleTransformerMapper transformerMapper;

    @Autowired
    private QueryDeviceInfoImpl queryDeviceInfo;

    private final Map<String, TaskInfo> taskMap = new ConcurrentHashMap<>();
    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    // =========================== 方案基本增删改查 =======================
    @Override
    public List<Node> getMainPath(String feederId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ArrayList<Node> result = new ArrayList<>();
        for (ArrayList<Node> mainNodePath : nodePath.getMainNodePaths()) {
            result.addAll(mainNodePath);
        }
        return ListUtils.distinctByKey(result, Node::getId);
    }

    @Override
    public ArrayList<SegBetween> getSegBetweenList(String feederId, String deviceId) {
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        // 批量处理配变容量信息
        List<SegBetween> segBetweenList = singAnalysis.getNodePath().getSegBetweenList();
        // 收集所有配变节点按类型分组
        Map<String, List<Node>> pbNodesByType = new HashMap<>();
        for (SegBetween segBetween : segBetweenList) {
            List<Node> allPb = segBetween.getAllPb();
            for (Node node : allPb) {
                if (node.isPb()) {
                    pbNodesByType.computeIfAbsent(node.getPsrType(), k -> new ArrayList<>()).add(node);
                }
            }
            segBetween.setPbNum(allPb.size());
        }
        // 批量查询并设置容量
        setCapacityBatch(pbNodesByType);

        return new ArrayList<>(segBetweenList);
    }


    @Override
    public List<BranchNodeVo> getBigBranch(String feederId) {
        // 查询拓扑信息
        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        if (singAnalysis == null) {
            log.warn("未获取到线路{}的单线图分析结果", feederId);
            return Collections.emptyList();
        }
        NodePath nodePath = singAnalysis.getNodePath();
        List<BranchNode> branchNodes = nodePath.getBranchNodeMap().values().stream().filter(n -> !n.isMain()).collect(Collectors.toList());

        // 处理分支节点的配变信息和起始结束设备信息
        handleBranchNodeInfo(branchNodes);

        // 封装BranchNodeVo
        List<BranchNodeVo> result = branchNodes.stream().filter(n -> StringUtils.isNotBlank(n.getPsrId())).map(n -> {
            Node startNode = n.getStartNode();
            if (startNode == null) {
                return null;
            }
            BranchNodeVo vo = new BranchNodeVo();
            vo.setId(startNode.getId());

            vo.setPsrName(startNode.getPsrName());
            vo.setPsrId(startNode.getPsrId());
            vo.setPsrType(startNode.getPsrType());

            vo.setPbNodes(NodeUtils.toNodeVos(n.getPbNodes()));
            vo.setPbNum(n.getPbNodes().size());
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return result;
    }


    private void setCapacityBatch(Map<String, List<Node>> pbNodesByType) {
        // 批量处理0302类型
        List<Node> type0302Nodes = pbNodesByType.get("0302");
        if (CollectionUtils.isNotEmpty(type0302Nodes)) {
            List<String> ids = type0302Nodes.stream().map(Node::getPsrId).collect(Collectors.toList());
            LambdaQueryWrapper<DeviceStationTransformer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DeviceStationTransformer::getPsrId, ids);
            List<DeviceStationTransformer> transformers = deviceStationTransformerMapper.selectList(queryWrapper);
            Map<String, DeviceStationTransformer> capacityMap = transformers.stream().collect(Collectors.toMap(DeviceStationTransformer::getPsrId, n -> n));

            type0302Nodes.forEach(node -> {
                DeviceStationTransformer deviceStationTransformer = capacityMap.get(node.getPsrId());
                if (Objects.isNull(deviceStationTransformer)) {
                    return;
                }
                String capacity = deviceStationTransformer.getRatedCapacity();
                node.setPubPrivFlag(deviceStationTransformer.getPubPrivFlag());
                if (StringUtils.isNotBlank(capacity)) {
                    try {
                        node.setCap(Double.parseDouble(capacity));
                    } catch (NumberFormatException e) {
                        log.warn("配变容量解析失败: psrId={}, capacity={}", node.getPsrId(), capacity);
                    }
                }
                // 公变的joinEc关联中压用户接入点
                String joinEc = deviceStationTransformer.getJoinEc();
                if (StringUtils.isNotBlank(joinEc)) {
                    DeviceAccessPoint deviceAccessPoint = deviceAccessPointMapper.selectById(joinEc);
                    if (deviceAccessPoint == null) {
                        return;
                    }
                    node.setPsrName(node.getPsrName() + "_" + deviceAccessPoint.getName());
                }
            });
        }

        // 批量处理0303类型
        List<Node> type0303Nodes = pbNodesByType.get("0303");
        if (CollectionUtils.isNotEmpty(type0303Nodes)) {
            List<String> ids = type0303Nodes.stream().map(Node::getPsrId).collect(Collectors.toList());
            LambdaQueryWrapper<StationServiceTransformer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(StationServiceTransformer::getPsrId, ids);
            List<StationServiceTransformer> transformers = stationServiceTransformerMapper.selectList(queryWrapper);
            Map<String, StationServiceTransformer> capacityMap = transformers.stream().collect(Collectors.toMap(StationServiceTransformer::getPsrId, n -> n));

            type0303Nodes.forEach(node -> {
                StationServiceTransformer stationServiceTransformer = capacityMap.get(node.getPsrId());
                if (Objects.isNull(stationServiceTransformer)) {
                    return;
                }
                String ratedCapacity = stationServiceTransformer.getRatedCapacity();
                node.setPubPrivFlag(stationServiceTransformer.getPubPrivFlag());
                if (StringUtils.isNotBlank(ratedCapacity)) {
                    try {
                        node.setCap(Double.parseDouble(ratedCapacity));
                    } catch (NumberFormatException e) {
                        log.warn("配变容量解析失败: psrId={}, capacity={}", node.getPsrId(), ratedCapacity);
                    }
                }
                // 公变的joinEc关联中压用户接入点
                String joinEc = stationServiceTransformer.getJoinEc();
                if (StringUtils.isNotBlank(joinEc)) {
                    DeviceAccessPoint deviceAccessPoint = deviceAccessPointMapper.selectById(joinEc);
                    if (deviceAccessPoint == null) {
                        return;
                    }
                    node.setPsrName(node.getPsrName() + "_" + deviceAccessPoint.getName());
                }
            });
        }
        // 处理0110
        List<Node> type0110Nodes = pbNodesByType.get("0110");
        if (CollectionUtils.isNotEmpty(type0110Nodes)) {
            LambdaQueryWrapper<DevicePoleTransformer> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DevicePoleTransformer::getPsrId, type0110Nodes.stream().map(Node::getPsrId).collect(Collectors.toList()));
            List<DevicePoleTransformer> transformers = transformerMapper.selectList(queryWrapper);
            Map<String, DevicePoleTransformer> capacityMap = transformers.stream().collect(Collectors.toMap(DevicePoleTransformer::getPsrId, n -> n));
            type0110Nodes.forEach(node -> {
                DevicePoleTransformer devicePoleTransformer = capacityMap.get(node.getPsrId());
                if (Objects.isNull(devicePoleTransformer)) {
                    return;
                }
                String ratedCapacity = devicePoleTransformer.getRatedCapacity();
                node.setPubPrivFlag(devicePoleTransformer.getPubPrivFlag());
                if (StringUtils.isNotBlank(ratedCapacity)) {
                    try {
                        node.setCap(Double.parseDouble(ratedCapacity));
                        return;
                    } catch (NumberFormatException e) {
                        log.warn("配变容量解析失败: psrId={}, capacity={}", node.getPsrId(), ratedCapacity);
                    }
                }
                String joinEc = devicePoleTransformer.getJoinEc();
                if (StringUtils.isNotBlank(joinEc)) {
                    DeviceAccessPoint deviceAccessPoint = deviceAccessPointMapper.selectById(joinEc);
                    if (deviceAccessPoint == null) {
                        return;
                    }
                    node.setPsrName(node.getPsrName() + "_" + deviceAccessPoint.getName());
                }
            });
        }
    }


    /**
     * 根据问题ID 查询方案列表
     */
    @Override
    public PlanGroupResultVo queryByProblemId(Long problemId) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        List<Plan> plans = planMapper.queryByProblemId(problemId);
        ProblemVo problemVo = problemMapper.selectVoById(problemId);
        PlanGroupResultVo planGroupResultVo = groupPlans(plans, problemVo.getCategoryLevel2Code(), problemId);
        return planGroupResultVo;
       /*  List<Map<String, Object>> list = planMapper.queryByProblemId(problemId);
//        List<Plan> collect = list.stream().map(map -> objectMapper.convertValue(map, Plan.class)).collect(Collectors.toList());
       return list.stream().map(map -> {
            Plan plan = new Plan();
            plan.setDisadvantage(map.get("disadvantage") != null ? (String) map.get("disadvantage") : null);
            plan.setExe(map.get("exe") != null ? (String) map.get("exe") : null);
            plan.setN1(map.get("n1") != null ? (String) map.get("n1") : null);
            plan.setAdvantage(map.get("advantage") != null ? (String) map.get("advantage") : null);
            plan.setProblemId(map.get("problem_id") != null ? (Long) map.get("problem_id") : null);
            plan.setBudget(map.get("budget") != null ? (BigDecimal) map.get("budget") : null);
            plan.setEconomy(map.get("economy") != null ? (String) map.get("economy") : null);
            plan.setOperateData(map.get("operate_data") != null ? (String) map.get("operate_data") : null);
            plan.setId(map.get("id") != null ? (Long) map.get("id") : null);
            plan.setReliability(map.get("reliability") != null ? (String) map.get("reliability") : null);
            plan.setRequireMents(map.get("require_ments") != null ? (Long) map.get("require_ments") : null);
            plan.setPlanType(map.get("plan_type") != null ? (String) map.get("plan_type") : null);
            // 特殊处理 load 字段（PGobject）
            Object loadValue = map.get("load");
            if (loadValue != null) {
                plan.setLoad(loadValue instanceof PGobject ? ((PGobject) loadValue).getValue() : loadValue.toString());
            } else {
                plan.setLoad(null);
            }
            return plan;
        }).collect(Collectors.toList());*/
    }

    /**
     * 查询故障解决方案
     */
    @Override
    public PlanVo queryById(Long id) {
        return planMapper.selectVoById(id);
    }

    /**
     * 查询故障解决方案列表
     */
    @Override
    public TableDataInfo<PlanVo> queryPageList(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = buildQueryWrapper(bo);
        PageQuery pageQuery = new PageQuery();
        pageQuery.setPageSize(bo.getPageSize());
        pageQuery.setPageNum(bo.getPageNum());
        Page<PlanVo> result = planMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询故障解决方案列表
     */
    @Override
    public List<PlanVo> queryList(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = buildQueryWrapper(bo);
        return planMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Plan> buildQueryWrapper(PlanBo bo) {
        LambdaQueryWrapper<Plan> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getLoad()), Plan::getLoad, bo.getLoad());
        lqw.eq(StringUtils.isNotBlank(bo.getEconomy()), Plan::getEconomy, bo.getEconomy());
        lqw.eq(StringUtils.isNotBlank(bo.getReliability()), Plan::getReliability, bo.getReliability());
        lqw.eq(StringUtils.isNotBlank(bo.getExe()), Plan::getExe, bo.getExe());
        lqw.eq(StringUtils.isNotBlank(bo.getAdvantage()), Plan::getAdvantage, bo.getAdvantage());
        lqw.eq(StringUtils.isNotBlank(bo.getDisadvantage()), Plan::getDisadvantage, bo.getDisadvantage());
        lqw.le(bo.getBudgetMax() != null, Plan::getBudget, bo.getBudgetMax());
        lqw.ge(bo.getBudgetMin() != null, Plan::getBudget, bo.getBudgetMin());
        lqw.eq(StringUtils.isNotBlank(bo.getN1()), Plan::getN1, bo.getN1());
        lqw.eq(StringUtils.isNotBlank(bo.getPlanType()), Plan::getPlanType, bo.getPlanType());
        lqw.eq(StringUtils.isNotBlank(bo.getOperateData()), Plan::getOperateData, bo.getOperateData());
        lqw.eq(StringUtils.isNotBlank(bo.getOperate()), Plan::getOperate, bo.getOperate());
        lqw.eq(bo.getRequireMents() != null, Plan::getRequireMents, bo.getRequireMents());

        return lqw;
    }

    /**
     * 新增解决方案
     */
    @Override
    public Boolean insertByBo(PlanBo bo) {
        Plan add = BeanUtil.toBean(bo, Plan.class);
        validEntityBeforeSave(add);
        boolean flag = planMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }


    /**
     * 修改故障解决方案
     */
    @Override
    public Boolean updateByBo(PlanBo bo) {
        Plan update = BeanUtil.toBean(bo, Plan.class);
        if (planMapper.selectCount(new QueryWrapper<Plan>().eq("id", bo.getId())) > 0) {
            validEntityBeforeSave(update);
            return planMapper.updateById(update) > 0;
        }
        return false;
    }


    /**
     * 按问题id查询故障解决方案
     */

    @Override
    public List<PlanVo> byProblemId(Long id) {
        LambdaQueryWrapper<Plan> lqw = new LambdaQueryWrapper<>();
        lqw.eq(Plan::getProblemId, id);
        return planMapper.selectVoList(lqw);
    }

    /**
     * 批量删除故障解决方案
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return planMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(Plan entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    // =========================== 方案生成 =======================

    /**
     * 网架方案生产过程
     */
    public List<Plan> generateGridPlan(GeneratePlanBo generatePlanBo) {
        Long problemId = generatePlanBo.getProblemId();
        String token = generatePlanBo.getToken();

        ProblemVo problemVo = problemMapper.selectVoById(problemId);
        String feederId = problemVo.getFeederId();

        Integer categoryLevel2Code = problemVo.getCategoryLevel2Code();
        String deviceId = problemVo.getDeviceId();

        IGeneratePlan gridPlantHandle = getGridPlantHandle(categoryLevel2Code);
        if (gridPlantHandle == null) {
            throw new RuntimeException("暂无当前问题分类的处理程序 ！");
        }

        return gridPlantHandle.generatePlan(problemId, deviceId, feederId, token);
    }

    /**
     * 根据问题类型获取网架方案的处理程序
     */
    private IGeneratePlan getGridPlantHandle(Integer type) {
        HashMap<Integer, IGeneratePlan> hashMap = new HashMap<Integer, IGeneratePlan>() {{
            put(PlanConstants.SEG_PB_MUSH_LEVEL, segPgMuchPlan); // 分段内配变不合理
            put(PlanConstants.SEG_DFSXL_MUSH_LEVEL, singleRadiationPlan); // 单辐射无联络
            put(PlanConstants.SEG_DFZWLN_MUSH_LEVEL, bigBranchPlan); // 大分子无联络
            put(PlanConstants.SEG_XLZGZ_MUSH_LEVEL, feederOverloadPlan); // 线路重过载
            put(PlanConstants.SEG_XLGJPB_MUSH_LEVEL, feederPbMuchPlan); // 线路挂接配变过多
            put(PlanConstants.SEG_TMLN_MUSH_LEVEL, sameBusContactPlan); // 同母联络线方案
        }};
        return hashMap.get(type);
    }

    /**
     * 按方案类型对方案进行分组，返回结构化的分组结果
     *
     * @param plans 原始方案列表
     * @param categoryLevel2Code 问题分类编码
     * @param problemId 问题ID
     * @return 分组结果
     */
    private PlanGroupResultVo groupPlans(List<Plan> plans, Integer categoryLevel2Code, Long problemId) {
        // 分组对象
        PlanGroupResultVo result = new PlanGroupResultVo(problemId, categoryLevel2Code);
        // 定义固定的分组顺序
        String[] orderedCategories = new String[]{
                PlanOperate.RUN_ADJUST_TYPE,      // 运行调整
                PlanOperate.REPLACE_LAY_TYPE,     // 间隔替换
                PlanOperate.SEG_KG_TYPE,          // 分段开关
                PlanOperate.CONTACT_TYPE,         // 联络线
                PlanOperate.BDZ_NEW_LINE_TYPE     // 变电站新出线
        };

        // 按planType分组现有方案
        Map<String, List<Plan>> planGroups = plans.stream()
                .filter(plan -> StringUtils.isNotBlank(plan.getPlanType()))
                .collect(Collectors.groupingBy(Plan::getPlanType));

        // 创建有方案的分组
        List<PlanGroupVo> groupsWithPlans = new ArrayList<>();
        // 创建无方案的分组
        List<PlanGroupVo> groupsWithoutPlans = new ArrayList<>();
        // 按固定顺序处理分组
        for (String category : orderedCategories) {
            List<Plan> categoryPlans = planGroups.getOrDefault(category, new ArrayList<>());
            PlanGroupVo group = new PlanGroupVo(category, getCategoryDescription(category));
            group.addPlans(categoryPlans);
            // 根据是否有方案分别加入不同列表
            if (!categoryPlans.isEmpty()) {
                groupsWithPlans.add(group);
            } else {
                groupsWithoutPlans.add(group);
            }
        }
        // 先添加有方案的分组
        for (PlanGroupVo group : groupsWithPlans) {
            result.addGroup(group);
        }
        // 再添加无方案的分组
        for (PlanGroupVo group : groupsWithoutPlans) {
            result.addGroup(group);
        }
        return result;
    }

    /**
     * 根据问题类型获取对应的固定解决方式分类
     *
     * @param categoryLevel2Code 问题分类编码
     * @return 固定的解决方式分类列表
     */
    private List<String> getRequiredCategoriesByProblemType(Integer categoryLevel2Code) {
        List<String> categories = new ArrayList<>();

        if (categoryLevel2Code == null) {
            return categories;
        }
        switch (categoryLevel2Code) {
            case PlanConstants.SEG_PB_MUSH_LEVEL: // 分段内配变数量不合理
                categories.add(PlanOperate.SEG_KG_TYPE);
                categories.add(PlanOperate.CONTACT_TYPE);
                categories.add(PlanOperate.BDZ_NEW_LINE_TYPE);
                break;
            case PlanConstants.SEG_DFZWLN_MUSH_LEVEL: // 大分支无联络
                categories.add(PlanOperate.CONTACT_TYPE);
                categories.add(PlanOperate.BDZ_NEW_LINE_TYPE);
                break;
            case PlanConstants.SEG_XLZGZ_MUSH_LEVEL: // 线路重过载
                categories.add(PlanOperate.RUN_ADJUST_TYPE);
                categories.add(PlanOperate.BDZ_NEW_LINE_TYPE);
                categories.add(PlanOperate.CONTACT_TYPE);
                break;
            case PlanConstants.SEG_XLGJPB_MUSH_LEVEL: // 线路挂接配变过多
                categories.add(PlanOperate.RUN_ADJUST_TYPE);
                categories.add(PlanOperate.BDZ_NEW_LINE_TYPE);
                categories.add(PlanOperate.CONTACT_TYPE);
                break;
            case PlanConstants.SEG_TMLN_MUSH_LEVEL: // 同母单联络
                categories.add(PlanOperate.REPLACE_LAY_TYPE);
                categories.add(PlanOperate.CONTACT_TYPE);
                break;
            case PlanConstants.SEG_DFSXL_MUSH_LEVEL: // 单辐射无联络
                categories.add(PlanOperate.CONTACT_TYPE);
                categories.add(PlanOperate.BDZ_NEW_LINE_TYPE);
                break;
            default:
                log.warn("未定义的问题类型: {}", categoryLevel2Code);
                break;
        }
        return categories;
    }

    /**
     * 获取方案分类的中文描述
     *
     * @param category 方案分类
     * @return 分类描述
     */
    private String getCategoryDescription(String category) {
        if (StringUtils.isBlank(category)) {
            return "未分类";
        }
        switch (category) {
            case "segKgType":
                return "分段开关方案";
            case "contactType":
                return "联络方案";
            case "bdzNewLineType":
                return "变电站新出线方案";
            case "runAdjustType":
                return "运行调整方案";
            case "replaceLayType":
                return "替换间隔方案";
            default:
                return category;
        }
    }

    /**
     * 异步生产方案，同时存入该次方案的状态实例对象
     */
    public String analysisGeneratePlan(GeneratePlanBo generatePlanBo) {

        TaskInfo taskInfo = new TaskInfo();

        //先判断关于此次的问题有没有相关实列正在执行中，如果有直接返回实列id，如果没有正在运行的实列则进行下一步
        LambdaQueryWrapper<PlanAnalysisState> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanAnalysisState::getProblemId, generatePlanBo.getProblemId());
        lambdaQueryWrapper.eq(PlanAnalysisState::getState, STATE_ANALYSIS_IN_PROGRESS);
        PlanAnalysisState initialization = planAnalysisStateMapper.selectOne(lambdaQueryWrapper);
//        if (initialization != null) {
//            //检查方案状态正在执行中的持续时间如果超过五分钟，如果没有超过5分钟的则返回实例id，如果超过5分钟则重新开始
//            if (planAnalysisStateMapper.countOverdueRecords(initialization.getId()) == 0) {
//                return initialization.getId();
//            }
//        }

        planAnalysisStateMapper.deleteByProblemId(generatePlanBo.getProblemId());

        //创建并且存入该次方案的状态实例对象
        PlanAnalysisState planAnalysisState = new PlanAnalysisState();
        planAnalysisState.setState(STATE_ANALYSIS_IN_PROGRESS);
        planAnalysisState.setProblemId(generatePlanBo.getProblemId());
        planAnalysisState.setStartTime(new Date());
        planAnalysisState.setVersion(generatePlanBo.getVersion());
        planAnalysisStateMapper.insert(planAnalysisState);

        taskMap.put(planAnalysisState.getId(), taskInfo);
        //开启异步生成方案
        Future<?> future = executorService.submit(() -> {
            try {
                if (!taskInfo.isPaused()) {
                    //生成方案列表，保存方案列表
                    Long problemId = generatePlanBo.getProblemId();
                    // 先删除
                    planProcessService.deleteByProblemId(problemId);
                    planMapper.deleteByProblemId(problemId);
                    List<Plan> plans = generatePlan(generatePlanBo);
                    //新增方案到数据库
                    planMapper.insertBatchPlan(plans);
                    //修改实例的状态完成
                    update(planAnalysisState.getId(), STATE_ANALYSIS_COMPLETED, "完成");
                    return null; // 返回null表示任务正常结束
                } else {
                    synchronized (taskInfo.getPauseLock()) {
                        taskInfo.getPauseLock().wait();
                    }
                }
                return null; // 显式返回null
            } catch (PlanGenerationException e) {
                // 方案生成阶段的业务异常
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, e.getMessage());
                return false;
            } catch (InterruptedException e) {
                // 任务被中断
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, "任务被中断");
                return false;
            } catch (Exception e) {
                // 其他未知异常
                log.error("方案生成过程中发生未知异常", e);
                update(planAnalysisState.getId(), STATE_ANALYSIS_ERROR, "未知异常");
                return false;
            }

        });


        taskInfo.setFuture(future);
        return planAnalysisState.getId();
    }

    /**
     * 生成方案
     *
     * @param generatePlanBo
     * @return
     */
    private List<Plan> generatePlan(GeneratePlanBo generatePlanBo) {
        Long problemId = generatePlanBo.getProblemId();
        List<Plan> plans = new ArrayList<>(); // 初始化列表避免NPE

        try {
            planProcessService.pushLoadingProcess(problemId, "开始生成方案");

            // 1. 方案生成阶段
            try {
                plans = generateGridPlan(generatePlanBo);
                if (CollectionUtils.isEmpty(plans)) {
                    throw new PlanGenerationException("生成的方案列表为空");
                }
            } catch (PlanGenerationException e) {
                log.error("方案生成失败：{}", e.getMessage(), e);
                // throw e; // 返回空列表或根据需求处理
            }

            // 2. 造价计算阶段
            try {
                for (Plan plan : plans) {
                    PlanCost planCost = costService.computeCost(plan);
                    //如果返回null说明方案中的实体解析成node出问题了
                    if (planCost == null) {
                        throw new PlanGenerationException("方案 " + plan.getId() + " 造价计算失败");
                    }
                    //如果总造价为空，说明没用到电网设备此方案不花钱
                    if (planCost.getTotalCost() == null) {
                        plan.setBudget(new BigDecimal("0.0"));
                    } else {
                        plan.setBudget(BigDecimal.valueOf(planCost.getTotalCost()));
                    }
                }
            } catch (PlanGenerationException e) {
                log.error("造价计算失败：{}", e.getMessage(), e);
                // throw e; // 或根据需求处理部分失败的方案
            }

            // 所有步骤成功完成
            planProcessService.pushProcess(new ProblemSchemeAnalysis(problemId, null, ProblemSchemeAnalysis.END_TYPE, null, "完成"));
            return plans;

        } catch (Exception e) {
            // 未知异常处理
            log.error("方案生成过程中发生未知异常：{}", e.getMessage(), e);
            throw new PlanGenerationException("方案生成失败：未知错误", e);
        }
    }


    // =========================== 相关方案任务 =======================


//    interface NodeTypeStrategy {
//        boolean isMatch(Node node);
//    }

    /**
     * 暂停任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean pauseTask(Long taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }
        taskInfo.setPaused(true);

        return true;
    }

    /**
     * 恢复任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean resumeTask(Long taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }

        if (taskInfo.isPaused()) {
            taskInfo.setPaused(false);
            synchronized (taskInfo.getPauseLock()) {
                taskInfo.getPauseLock().notify();
            }
        }

        return true;
    }

    /**
     * 停止任务
     *
     * @param taskId
     * @return
     */
    @Override
    public Boolean stopTask(String taskId) {
        TaskInfo taskInfo = taskMap.get(taskId);
        if (taskInfo == null) {
            return false;
        }

        taskInfo.setCancelled(true);
        taskInfo.getFuture().cancel(true);
        taskMap.remove(taskId);
        update(taskId, STATE_ANALYSIS_STOPPED, "任务暂停");

        return true;
    }

    private void update(String taskId, int state, String message) {
        LambdaQueryWrapper<PlanAnalysisState> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(PlanAnalysisState::getId, taskId);
        PlanAnalysisState update = new PlanAnalysisState();
        update.setState(state);
        update.setEndTime(new Date());
        update.setMessage(message);
        planAnalysisStateMapper.update(update, lambdaQueryWrapper);
    }

    /**
     * 根据问题id查询状态实例
     */
    @Override
    public PlanAnalysisState selectState(String id) {


        return planAnalysisStateMapper.selectById(id);
    }

    /**
     * 根据方案id查询所有相关方案造价
     *
     * @param id
     * @return
     */
    @Override
    public PlanCost planCost(Long id) throws JsonProcessingException {
        Plan plan = planMapper.selectById(id);
        if (plan != null) {

            return costService.computeCost(plan);
        }
        return null;
    }


    /**
     * 根据问题id查询馈线，根据馈线查询对应的联络线，以及联络开关
     * 只返回同一个母线下的结果
     * @param problemId
     * @return
     */
    @Override
    public List<ContactSwitchVo> getBusbarSwitchByProblemId(Long problemId) {
        LambdaQueryWrapper<Problem> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Problem::getProblemId, problemId);
        Problem problem = problemMapper.selectOne(queryWrapper);
        if (problem == null) {
            return Collections.emptyList();
        }
        String feederId = problem.getFeederId();

        // 获取主馈线的母线信息
        BusbarSwitchVo mainFeederBusbar = bayQueryService.queryLineTopology(feederId);
        if (mainFeederBusbar == null || StringUtils.isBlank(mainFeederBusbar.getBusbarId())) {
            return Collections.emptyList();
        }
        String mainBusbarId = mainFeederBusbar.getBusbarId();

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        List<ContactFeederKg> contactFeederKgs = nodePath.getContactFeederKgs();
        List<Node> contactKgNodes = singAnalysis.getNodePath().getContactKgNodes();
        // key 为psrId value为node
        Map<String, Node> contactKgNodeMap = contactKgNodes.stream().collect(Collectors.toMap(Node::getPsrId, d -> d));
        // 联络开关和联络线 关联在一起
        if (CollectionUtils.isNotEmpty(contactFeederKgs)) {
            List<ContactSwitchVo> result = new ArrayList<>();
            for (ContactFeederKg contactFeederKg : contactFeederKgs) {
                String contactFeederId = contactFeederKg.getFeederPsrId();
                if (StringUtils.isEmpty(contactFeederId)) {
                    continue;
                }
                BusbarSwitchVo busbarSwitchVo = bayQueryService.queryLineTopology(contactFeederId);
                if (busbarSwitchVo == null) {
                    continue;
                }
                // 只返回同一个母线下的结果
                if (StringUtils.equals(mainBusbarId, busbarSwitchVo.getBusbarId())) {
                    Node contact = contactKgNodeMap.get(contactFeederKg.getKgPsrId());
                    // 设置联络开关信息
                    ContactSwitchVo contactSwitchVo = new ContactSwitchVo();
                    contactSwitchVo.setContactSwitchId(contactFeederKg.getKgPsrId());
                    contactSwitchVo.setContactSwitchName(contact.getPsrName());
                    contactSwitchVo.setContactSwitchType(contactFeederKg.getKgPsrType());
                    contactSwitchVo.setFeederName(busbarSwitchVo.getFeederName());
                    contactSwitchVo.setBusbarName(busbarSwitchVo.getBusbarName());
                    result.add(contactSwitchVo);
                }
            }
            return result;
        }
        return Collections.emptyList();
    }

    /**
     * 处理分支节点的配变信息和起始结束设备信息
     */
    private void handleBranchNodeInfo(List<BranchNode> branchNodes) {
        try {
            Map<String, List<Node>> pbNodesByType = new HashMap<>();
            for (BranchNode branchNode : branchNodes) {
                String psrName = branchNode.getPsrName();
                // 获取当前分支下的配变节点
                List<Node> pbNodes = branchNode.getPbNodes();
                // 设置配变数量
                branchNode.setPbNum(pbNodes.size());

                // 收集配变节点按类型分组
                for (Node node : pbNodes) {
                    if (node.isPb()) {
                        pbNodesByType.computeIfAbsent(node.getPsrType(), k -> new ArrayList<>()).add(node);
                    }
                }
            }
            // 计算每个分支的配变总容量和公专变信息
            for (BranchNode branchNode : branchNodes) {
                calculateBranchPbInfo(branchNode);
            }
        } catch (Exception e) {
            log.error("处理分支节点信息异常", e);
        }
    }


    /**
     * 处理分支节点的配变信息（容量、公专变信息）
     */
    private void calculateBranchPbInfo(BranchNode branchNode) {
        try {
            // 获取当前分支下的配变节点
            List<Node> pbNodes = branchNode.getPbNodes();
            if (CollectionUtils.isEmpty(pbNodes)) {
                return;
            }
            // 提取配变设备ID列表
            List<String> pbIdList = pbNodes.stream().map(Node::getPsrId).filter(Objects::nonNull).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(pbIdList)) {
                branchNode.setPbNum(0);
                return;
            }
            // 查询配变信息
            List<PBEntity> pbEntities = queryDeviceInfo.selectDevice(pbIdList);
            // 为pbNodes设置容量、公专变信息
            Map<String, PBEntity> pbMap = pbEntities.stream().collect(Collectors.toMap(PBEntity::getPsrId, n -> n));
            for (Node pbNode : pbNodes) {
                PBEntity pbEntity = pbMap.get(pbNode.getPsrId());
                if (pbEntity != null) {
                    if (StringUtils.isEmpty(pbEntity.getRatedCapacity())) {
                        continue;
                    }
                    pbNode.setCap(Double.parseDouble(pbEntity.getRatedCapacity()));
                    pbNode.setPubPrivFlag(pbEntity.getPubPrivFlag());
                    // 公变的joinEc关联中压用户接入点
                    String joinEc = pbEntity.getJoinEc();
                    if (StringUtils.isNotBlank(joinEc)) {
                        DeviceAccessPoint deviceAccessPoint = deviceAccessPointMapper.selectById(joinEc);
                        if (deviceAccessPoint == null) {
                            continue;
                        }
                        pbNode.setPsrName(pbNode.getPsrName() + "_" + deviceAccessPoint.getName());
                    }
                }
            }
            branchNode.setPbNum(pbEntities.size());
        } catch (Exception e) {
            log.error("处理分支节点配变信息异常", e);
        }
    }

    @Resource
    private DeviceAccessPointMapper deviceAccessPointMapper;

}
