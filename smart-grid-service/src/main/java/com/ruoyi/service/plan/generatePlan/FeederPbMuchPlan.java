package com.ruoyi.service.plan.generatePlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.entity.calc.CombFeederTransfer;
import com.ruoyi.entity.calc.FeederTransferCap;
import com.ruoyi.entity.device.DeviceFeeder;
import com.ruoyi.entity.device.vo.FeederNtVo;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.Plan;
import com.ruoyi.entity.znap.ContactFeederKg;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.service.plan.findLay.contactLay.ContactLayPlanOp;
import com.ruoyi.service.plan.findLay.contactLay.FindEndContactLay;
import com.ruoyi.service.plan.findLay.runAdjustLay.FindRunAdjustLay;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.service.plan.model.plan.*;
import com.ruoyi.mapper.device.FeederDeviceMapper;
import com.ruoyi.service.device.impl.QueryDeviceInfoImpl;
import com.ruoyi.service.device.impl.SegBranchServiceImpl;
import com.ruoyi.service.map.ISingMapService;
import com.ruoyi.service.plan.IConditionService;
import com.ruoyi.service.plan.IProcessNodeService;
import com.ruoyi.service.plan.IGeneratePlan;
import com.ruoyi.service.plan.impl.ContactHandleService;
import com.ruoyi.service.plan.impl.PlanProcessServiceImpl;
import com.ruoyi.service.plan.impl.PushPlanProcessServiceImpl;
import com.ruoyi.service.plan.model.CanRunAdjustFeeder;
import com.ruoyi.service.znap.IZnapTopologyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 线路挂接配变过多方案生成
 * 解决措施：
 * 1、超挂线路部分负荷运方调整至已有联络线路（转供）
 * 2、针对超挂线路负载率超80%情况，可考虑部分负荷切转至新出线路，切转点宜设置在超挂线路末端。（变电站新出线）
 * 3、针对超挂线路重要连接位置缺乏联络情况，考虑与临近线路新上联络，超挂线路部分负荷运方调整至该线路（新增联络）
 */
@Service
@Slf4j
public class FeederPbMuchPlan implements IGeneratePlan {

    @Autowired
    PlanProcessServiceImpl planProcessService;

    @Autowired
    FeederDeviceMapper feederDeviceMapper;

    @Autowired
    IZnapTopologyService znapTopologyService;

    @Autowired
    SegBranchServiceImpl segBranchService;

    @Autowired
    QueryDeviceInfoImpl queryDeviceInfo;

    @Autowired
    IProcessNodeService processNodeService;

    @Autowired
    BaseGeneratePlan baseGeneratePlan;

    @Autowired
    PushPlanProcessServiceImpl pushPlanProcessService;

    @Autowired
    ISingMapService singMapService;

    @Autowired
    IConditionService iConditionService;

    @Autowired
    ContactHandleService contactHandleService;

    @Autowired
    FindEndContactLay findEndContactLay;

    @Override
    public List<Plan> generatePlan(Long problemId, String deviceId, String feederId, String token) {


        // =========================== 基础信息准备 =======================
        LambdaQueryWrapper<DeviceFeeder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(DeviceFeeder::getPsrId, feederId);
        DeviceFeeder deviceFeeder = feederDeviceMapper.selectOne(lambdaQueryWrapper);
        FeederNtVo feederNt = feederDeviceMapper.selectFeederNtsByFeederId(feederId);

        SingAnalysis singAnalysis = singMapService.analysisSingMap(feederId, false);
        NodePath nodePath = singAnalysis.getNodePath();
        ZnapTopology topologyMap = singAnalysis.getTopologyMap();
        Map<String, Node> nodeMap = nodePath.getNodeMap();

        int maxPbNum = iConditionService.pylonsPBNum();// 最大的配变数量
        double maxLoad = contactHandleService.getMaxFeederLoad(); // // 当前配置最大负载率
        double currentLoad = feederNt.getHisMaxLoadRate();
        List<Node> allPb = nodePath.getPbList();

        // 当前问题的校验
        if (allPb.size() < maxPbNum) {
            throw new RuntimeException("当前配变总数为：" + allPb.size() + "台，并未大于" + maxPbNum + "，该线路不是线路配变过多问题！");
        }

        // 配变节点的容量加工
        baseGeneratePlan.processPbNodeCap(allPb);

        // (1)、推送：基本信息
        pushPlanProcessService.pushInfo(problemId, deviceFeeder, allPb);

        // (2)、推送：配变列表
        pushPlanProcessService.pushPbList(problemId, allPb);

        // (3)、推送：问题释义
        pushPlanProcessService.pushFeederPbMuchExplain(problemId, allPb.size(), maxPbNum);

        // (4)、推送：线路挂接配变过多识别
        pushPlanProcessService.pushFeederPbMuchIdentify(problemId, deviceFeeder, allPb.size(), maxPbNum, feederNt.getHisMaxLoadRateStr());

        SurePlanOp surePlanOp = new SurePlanOp();

        // =========================== 运放调整组合 =======================
        // 获取所有下联络线
        List<FeederNtVo> contactFeederNts = baseGeneratePlan.getContactFeederNts(nodePath);

        //  获取可以运方调整的联络线开关
        List<ContactFeederKg> canContactFeederKgs = contactHandleService.getCanContactFeederKg(feederId, nodePath, contactFeederNts, maxLoad);

        // 获取能运放调整开关的所有可能组合(负载率满足)
        CanRunAdjustFeeder canRunAdjust = contactHandleService.getCanRunAdjust(feederNt, canContactFeederKgs, contactFeederNts, nodePath, maxLoad, nodeMap);

        // 组合转供（只满足负载率）
        List<CombFeederTransfer> combCanFTrs = canRunAdjust.getCombCanFTrs();
        combCanFTrs = filterCombMeetPbNum(combCanFTrs, maxPbNum);// 过滤不满足转供配变数量

        // 单个转供（只满足负载率）
        List<List<FeederTransferCap>> singleCanFTrs = canRunAdjust.getSingleCanFTrs();
        singleCanFTrs = filterMeetPbNum(singleCanFTrs, maxPbNum);// 过滤不满足转供配变数量
        List<FeederTransferCap> runAdjustList = priorRunAdjust(singleCanFTrs);

        // 单运方调整的
        if (CollectionUtils.isNotEmpty(runAdjustList)) {
            surePlanOp.addAll(FindRunAdjustLay.findRunAdjustLay(runAdjustList, currentLoad, maxLoad).getPlanOps());
        }
        // 组合运方调整的
        if (CollectionUtils.isNotEmpty(combCanFTrs)) {
            surePlanOp.addAll(FindRunAdjustLay.findCombRunAdjustLay(combCanFTrs, currentLoad, maxLoad).getPlanOps());
        }

        // (5)、推送：附近相关联问题
        pushPlanProcessService.pushNearProblem(problemId);

        // (6)、推送：合并策略预结果
        pushPlanProcessService.pushMergeSolve(problemId);

        // =========================== 临近线路新上联络 =======================

        if (surePlanOp.getSize() < 3) {

            List<ContactBranch> tfBranch = findEndContactLay.getFeederLoad(nodePath.getStartNode(), nodePath.getStartNextEdge(), feederNt, maxLoad, nodePath, (pbs, changeLoad) -> allPb.size() - pbs.size() < maxPbNum);

            // 表示配变数量过不去 那么就不需要了
            if (CollectionUtils.isEmpty(tfBranch)) {
                tfBranch = findEndContactLay.getFeederLoad(nodePath.getStartNode(), nodePath.getStartNextEdge(), feederNt, maxLoad, nodePath);
            }

            CombAlternateOp combAlternateOp = ContactLayPlanOp.getEndContactCombAltOpByBranches(tfBranch, nodePath);

            // 给杆塔加装坐标
            baseGeneratePlan.layPositionCoords(combAlternateOp);

            SurePlanOp tmpSurePlanOp = null;
            try {
                tmpSurePlanOp = processNodeService.handleLayOperateNode(problemId, combAlternateOp, token, feederId, nodePath);
            } catch (Exception e) {
                log.error("生产方案异常！", e);
                throw new RuntimeException("生产方案异常！", e);
            }

            planProcessService.pushLoadingProcess(problemId, "方案集合按照开关数和联络距离排序");

            surePlanOp.addAll(tmpSurePlanOp.getPlanOps());
        }

        // =========================== 方案生成 =======================

        HashMap<Long, PlanOperate> planLaysMap = new HashMap<>();

        // 生成方案
        List<Plan> plans = surePlanOp.toPlans(problemId, planLaysMap);

        // (7)、推送：预方案生成
        pushPlanProcessService.pushPlans(problemId, plans, surePlanOp);

        // 返回所有方案，不限制数量
        List<Plan> resultPlans = plans;

        // (7)、推送：经济维度分析
        pushPlanProcessService.pushBudgetDim(problemId);

        // (8)、推送：推送施工与周期维度
        pushPlanProcessService.pushConstrCycleDim(problemId);

        // (8)、推送：约束条件匹配性
        pushPlanProcessService.pushConstraintMatchDim(problemId);

        // (9)、推送：综合推荐方案
        pushPlanProcessService.pushRecommendPlans(problemId, resultPlans, planLaysMap);

        // 获取各个分支末端
        return resultPlans;
    }

    // 过滤满足转供的组合（转供联络线配变限制和当前线路配变线路限制）
    List<List<FeederTransferCap>> filterMeetPbNum(List<List<FeederTransferCap>> singleCanFTrs, int maxPbNum) {
        if (CollectionUtils.isEmpty(singleCanFTrs)) {
            return singleCanFTrs;
        }
        List<List<FeederTransferCap>> result = new ArrayList<>();
        for (List<FeederTransferCap> canFTr : singleCanFTrs) {
            // 过滤
            List<FeederTransferCap> fTrs = canFTr.stream().filter(fTr -> {
                // 转供联络线路之后 和 当前线路转供 后 配变数量小于最大
                return fTr.getTfContactAfterPbNum() < maxPbNum && fTr.getSourceAfterPbNum() < maxPbNum;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(fTrs)) {
                result.add(fTrs);
            }
        }
        return result;
    }

    // 过滤满足转供的组合（转供联络线配变限制和当前线路配变线路限制）
    List<CombFeederTransfer> filterCombMeetPbNum(List<CombFeederTransfer> combCanFTrs, int maxPbNum) {
        if (CollectionUtils.isEmpty(combCanFTrs)) {
            return combCanFTrs;
        }
        List<CombFeederTransfer> result = new ArrayList<>();
        for (CombFeederTransfer combCanFTr : combCanFTrs) {

            // 按照转供线路进行分组 来进行判断 判断当前组合的线路是否有超过最大值
            boolean isCan = true;
            Map<String, List<FeederTransferCap>> feederGroup = combCanFTr.getFeederTransferCaps().stream().collect(Collectors.groupingBy(FeederTransferCap::getTfContactPsrId));
            // 判断其中是否有条线路不满足了
            for (List<FeederTransferCap> fTrs : feederGroup.values()) {
                int baseTfPbNum = fTrs.get(0).getTfContactPbNum();
                int baseSourcePbNum = fTrs.get(0).getSourcePbNum();
                int total = fTrs.stream().mapToInt(d -> d.getPbs().size()).sum();
                // 转供联络线路之后 和 当前线路转供 后 配变数量大于最大值   直接不满足
                if ((baseTfPbNum + total) > maxPbNum || (baseSourcePbNum - total) > maxPbNum) {
                    isCan = false;
                    break;
                }
            }
            if (isCan) {
                result.add(combCanFTr);
            }

        }
        return result;
    }

    /**
     * 更具运方调整的组合（单个开关有多个操作）
     * 我们按照一定的优先级 提取其中的一个即可
     */
    public List<FeederTransferCap> priorRunAdjust(List<List<FeederTransferCap>> allCanTransfer) {
        if (CollectionUtils.isEmpty(allCanTransfer)) {
            return null;
        }

        ArrayList<FeederTransferCap> result = new ArrayList<>();

        for (List<FeederTransferCap> transferCaps : allCanTransfer) {
            if (CollectionUtils.isEmpty(transferCaps)) {
                continue;
            }
            // 将当前联络开关的多个操作 我们取其中一个
            // 先排序(两条线专供之后的差值越小越优先) 在提取第一个
            transferCaps.sort((trf1, trf2) -> {
                double val1 = Math.abs(trf1.getSourceChangeLoad() - trf1.getTfContactChangeLoad());
                double val2 = Math.abs(trf2.getSourceChangeLoad() - trf2.getTfContactChangeLoad());
                return Double.compare(val1, val2);
            });
            result.add(transferCaps.get(0));
        }
        return result;
    }

}

